{"summary": {"totalTests": 94, "passedTests": 8, "failedTests": 0, "skippedTests": 86, "totalDuration": 6157, "success": false, "timestamp": "2025-06-20T22:42:55.527Z", "environment": "test"}, "projects": {"UNIT": {"tests": [{"title": "should generate menu link with token and orderURL", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["LinkGenerator", "generateMenuLink()"]}, {"title": "should handle different domains", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["LinkGenerator", "generateMenuLink()"]}, {"title": "should handle special characters in token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["LinkGenerator", "generateMenuLink()"]}, {"title": "should generate address link with token and orderURL", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["LinkGenerator", "generateAddressLink()"]}, {"title": "should handle different domains", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["LinkGenerator", "generateAddressLink()"]}, {"title": "should handle special characters in token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["LinkGenerator", "generateAddressLink()"]}, {"title": "should export a service instance", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Service Initialization"]}, {"title": "should have required methods", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Service Initialization"]}, {"title": "should have data storage maps", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Service Initialization"]}, {"title": "should be able to call initialize method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Basic Functionality"]}, {"title": "should be able to call getBrandRef method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Basic Functionality"]}, {"title": "should be able to call getRestaurantRef method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Basic Functionality"]}, {"title": "should be able to call getRestaurantsByBrand method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Basic Functionality"]}, {"title": "should have proper data structure", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Data Storage"]}, {"title": "should handle empty data gracefully", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["RestaurantStore", "Data Storage"]}, {"title": "should export a service instance", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Service Initialization"]}, {"title": "should have required methods", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Service Initialization"]}, {"title": "should have Redis client", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Service Initialization"]}, {"title": "should have session queues map", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Service Initialization"]}, {"title": "should be able to call initializeContext method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Basic Functionality"]}, {"title": "should be able to call createSession method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Basic Functionality"]}, {"title": "should be able to call getSession method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Basic Functionality"]}, {"title": "should be able to call updateSession method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Basic Functionality"]}, {"title": "should be able to call deleteSession method", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Basic Functionality"]}, {"title": "should initialize context with proper structure", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Context Initialization"]}, {"title": "should merge custom context with defaults", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionService", "Context Initialization"]}, {"title": "should generate an opaque token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"]}, {"title": "should generate unique tokens", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"]}, {"title": "should generate tokens with base64url format", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"]}, {"title": "should generate tokens of consistent length", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"]}, {"title": "should handle errors gracefully", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "generateOpaqueToken()"]}, {"title": "should validate a valid token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should reject null token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should reject undefined token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should reject non-string token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should reject empty string token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should reject invalid base64url token", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should reject token with wrong length", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "validateToken()"]}, {"title": "should generate cryptographically secure tokens", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "Token Security"]}, {"title": "should generate tokens that are URL-safe", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "Token Security"]}, {"title": "should generate tokens without padding", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["SessionIdGenerator", "Token Security"]}, {"title": "should build basic text message for notification", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"]}, {"title": "should build basic text message for dialog", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"]}, {"title": "should default to notification message type", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"]}, {"title": "should handle empty text message", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"]}, {"title": "should handle special characters in text", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildBasicTextMessageData()"]}, {"title": "should build quick reply message with buttons", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"]}, {"title": "should handle single button", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"]}, {"title": "should handle header and footer", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"]}, {"title": "should throw error for invalid button count", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildQuickReplyMessageData()"]}, {"title": "should build template message with variables", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildWhatsAppTemplateMessageData()"]}, {"title": "should handle template with image", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildWhatsAppTemplateMessageData()"]}, {"title": "should handle empty options", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildWhatsAppTemplateMessageData()"]}, {"title": "should build dialogue text message", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildDialogueMessageData()"]}, {"title": "should handle empty text", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "buildDialogueMessageData()"]}, {"title": "should create valid notification message structure", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "Message Structure Validation"]}, {"title": "should create valid dialogue message structure", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["MessageBuilders", "Message Structure Validation"]}, {"title": "R-STATUS-004: should update refundStatus to FULL after full refund", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Advanced Tests"]}, {"title": "R-STATUS-006: should support GraphQL refundStatus field queries", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Advanced Tests"]}, {"title": "R-STATUS-007: should maintain consistency between refundStatus and totalRefunded", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Advanced Tests"]}, {"title": "should have refundStatus default value as NONE", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Field Tests"]}, {"title": "should allow valid refundStatus enum values", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Field Tests"]}, {"title": "should reject invalid refundStatus values", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Field Tests"]}, {"title": "should verify refundStatus field definition in schema", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Field Tests"]}, {"title": "should verify refundStatus field exists in schema", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Simple Tests"]}, {"title": "should create order with minimal data and verify refundStatus default", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Simple Tests"]}, {"title": "should verify refundStatus can be set to valid enum values", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Simple Tests"]}, {"title": "should verify Order model has all refund-related fields", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Simple Tests"]}, {"title": "should demonstrate refundStatus functionality without complex ObjectIds", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order Model - refundStatus Simple Tests"]}, {"title": "should pass validation with valid token and session", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Session Validator Middleware"]}, {"title": "should return 401 when token is missing", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Session Validator Middleware"]}, {"title": "should return 401 when token format is invalid", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Session Validator Middleware"]}, {"title": "should return 404 when session does not exist", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Session Validator Middleware"]}, {"title": "should return 404 when session exists but dialogueId is missing", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Session Validator Middleware"]}, {"title": "should return 500 when internal error occurs", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Session Validator Middleware"]}, {"title": "should send welcome message with restaurant info and buttons", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order FSM Actions", "sendWelcomeMessage"]}, {"title": "should handle missing restaurant selection", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order FSM Actions", "sendWelcomeMessage"]}, {"title": "should throw error with invalid context", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order FSM Actions", "sendWelcomeMessage"]}, {"title": "should successfully send payment link", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order FSM Actions", "sendPaymentLink"]}, {"title": "should handle missing parameters", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order FSM Actions", "sendPaymentLink"]}, {"title": "should export a service instance", "status": "passed", "duration": 1251, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Service Initialization"]}, {"title": "should have required methods", "status": "passed", "duration": 72, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Service Initialization"]}, {"title": "should have configuration properties or be configurable", "status": "passed", "duration": 151, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Service Initialization"]}, {"title": "should have message queue or queueing capability", "status": "passed", "duration": 67, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Service Initialization"]}, {"title": "should have retry configuration or error handling", "status": "passed", "duration": 57, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Service Initialization"]}, {"title": "should be able to call getAccessToken method", "status": "passed", "duration": 295, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Basic Functionality"]}, {"title": "should be able to call sendBasicText method", "status": "passed", "duration": 70, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Basic Functionality"]}, {"title": "should be able to call sendQuickReply method", "status": "passed", "duration": 57, "failureMessages": [], "ancestorTitles": ["WhatsAppService", "Basic Functionality"]}, {"title": "should transition from initial to restaurant selection on message received", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "基础状态转换测试"]}, {"title": "should transition through the order flow", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "基础状态转换测试"]}, {"title": "should handle payment failure", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "基础状态转换测试"]}, {"title": "should start and stop dialog manager service", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"]}, {"title": "should properly clean up mocks and reset state", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"]}, {"title": "should handle unknown event types gracefully", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"]}], "summary": {"total": 94, "passed": 8, "failed": 0, "skipped": 86, "duration": 2020}}}, "coverage": {}, "performance": {}}