[2025-06-21T10:49:19.968Z] ================================================================================
[2025-06-21T10:49:19.968Z] Test Run Started
[2025-06-21T10:49:19.968Z] Timestamp: 2025-06-21T10:49:19.967Z
[2025-06-21T10:49:19.968Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:49:19.968Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:49:19.968Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:49:19.968Z] ================================================================================
[2025-06-21T10:49:19.972Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:49:19.972Z] Total Test Suites: 12
[2025-06-21T10:49:19.973Z] Test Environment: test
[2025-06-21T10:49:20.053Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:49:20.053Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.053Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:49:20.053Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.053Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:49:20.053Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.053Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:49:20.053Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:20.054Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:49:20.054Z] Suite Display Name: UNIT
[2025-06-21T10:49:21.772Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T10:49:21.773Z]   Module: restaurantstore
[2025-06-21T10:49:21.774Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T10:49:21.774Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.774Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.774Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T10:49:21.775Z]   Module: restaurantstore
[2025-06-21T10:49:21.775Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T10:49:21.775Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.775Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.775Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T10:49:21.775Z]   Module: restaurantstore
[2025-06-21T10:49:21.775Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T10:49:21.775Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.775Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.775Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T10:49:21.775Z]   Module: restaurantstore
[2025-06-21T10:49:21.775Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T10:49:21.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.776Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T10:49:21.776Z]   Module: restaurantstore
[2025-06-21T10:49:21.776Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T10:49:21.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.776Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T10:49:21.776Z]   Module: restaurantstore
[2025-06-21T10:49:21.776Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T10:49:21.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.776Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T10:49:21.776Z]   Module: restaurantstore
[2025-06-21T10:49:21.776Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T10:49:21.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.776Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T10:49:21.776Z]   Module: restaurantstore
[2025-06-21T10:49:21.776Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T10:49:21.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.777Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T10:49:21.777Z]   Module: restaurantstore
[2025-06-21T10:49:21.777Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T10:49:21.777Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.777Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.777Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:49:21.777Z] Suite Duration: 1479ms
[2025-06-21T10:49:21.777Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:49:21.847Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T10:49:21.847Z]   Module: sessionservice
[2025-06-21T10:49:21.847Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T10:49:21.847Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.847Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.847Z] 
  [CASE START] - ***********: should have required methods
[2025-06-21T10:49:21.847Z]   Module: sessionservice
[2025-06-21T10:49:21.847Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T10:49:21.847Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.847Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.848Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T10:49:21.848Z]   Module: sessionservice
[2025-06-21T10:49:21.848Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T10:49:21.848Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.848Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.848Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T10:49:21.848Z]   Module: sessionservice
[2025-06-21T10:49:21.848Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T10:49:21.848Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.848Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.848Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T10:49:21.848Z]   Module: sessionservice
[2025-06-21T10:49:21.848Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T10:49:21.848Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.848Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.849Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T10:49:21.849Z]   Module: sessionservice
[2025-06-21T10:49:21.849Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T10:49:21.849Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.849Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.849Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T10:49:21.849Z]   Module: sessionservice
[2025-06-21T10:49:21.849Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T10:49:21.849Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.849Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.849Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T10:49:21.849Z]   Module: sessionservice
[2025-06-21T10:49:21.849Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T10:49:21.849Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.849Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.849Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T10:49:21.849Z]   Module: sessionservice
[2025-06-21T10:49:21.849Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T10:49:21.849Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.849Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.849Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T10:49:21.850Z]   Module: sessionservice
[2025-06-21T10:49:21.850Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T10:49:21.850Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.850Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.850Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T10:49:21.850Z]   Module: sessionservice
[2025-06-21T10:49:21.850Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T10:49:21.850Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.850Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.850Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:49:21.850Z] Suite Duration: 1559ms
[2025-06-21T10:49:21.850Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T10:49:21.880Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T10:49:21.880Z]   Module: whatsapp
[2025-06-21T10:49:21.880Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T10:49:21.930Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T10:49:21.930Z]   Module: messagebuilders
[2025-06-21T10:49:21.930Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T10:49:21.930Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.930Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T10:49:21.930Z]   Module: messagebuilders
[2025-06-21T10:49:21.930Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T10:49:21.930Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.930Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T10:49:21.930Z]   Module: messagebuilders
[2025-06-21T10:49:21.930Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T10:49:21.930Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.931Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T10:49:21.931Z]   Module: messagebuilders
[2025-06-21T10:49:21.931Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T10:49:21.931Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.931Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.931Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T10:49:21.931Z]   Module: messagebuilders
[2025-06-21T10:49:21.931Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T10:49:21.931Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.931Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.931Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T10:49:21.931Z]   Module: messagebuilders
[2025-06-21T10:49:21.931Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T10:49:21.931Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.931Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.931Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T10:49:21.931Z]   Module: messagebuilders
[2025-06-21T10:49:21.931Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T10:49:21.931Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.931Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.932Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T10:49:21.932Z]   Module: messagebuilders
[2025-06-21T10:49:21.932Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T10:49:21.932Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.932Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.932Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T10:49:21.932Z]   Module: messagebuilders
[2025-06-21T10:49:21.932Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T10:49:21.932Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.932Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.932Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T10:49:21.932Z]   Module: messagebuilders
[2025-06-21T10:49:21.932Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T10:49:21.932Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.932Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.933Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T10:49:21.933Z]   Module: messagebuilders
[2025-06-21T10:49:21.933Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T10:49:21.933Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.933Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.933Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T10:49:21.933Z]   Module: messagebuilders
[2025-06-21T10:49:21.933Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T10:49:21.933Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.933Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.933Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T10:49:21.933Z]   Module: messagebuilders
[2025-06-21T10:49:21.933Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T10:49:21.933Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.933Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.933Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T10:49:21.933Z]   Module: messagebuilders
[2025-06-21T10:49:21.933Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T10:49:21.933Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.934Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.934Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T10:49:21.934Z]   Module: messagebuilders
[2025-06-21T10:49:21.934Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T10:49:21.934Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.934Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.934Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T10:49:21.934Z]   Module: messagebuilders
[2025-06-21T10:49:21.934Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T10:49:21.934Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.934Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.934Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:49:21.934Z] Suite Duration: 1661ms
[2025-06-21T10:49:21.934Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T10:49:21.937Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T10:49:21.937Z]   Module: linkgenerator
[2025-06-21T10:49:21.937Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T10:49:21.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.937Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T10:49:21.937Z]   Module: linkgenerator
[2025-06-21T10:49:21.937Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T10:49:21.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.937Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T10:49:21.937Z]   Module: linkgenerator
[2025-06-21T10:49:21.937Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T10:49:21.938Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.938Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.938Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T10:49:21.938Z]   Module: linkgenerator
[2025-06-21T10:49:21.938Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T10:49:21.938Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.938Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.938Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T10:49:21.938Z]   Module: linkgenerator
[2025-06-21T10:49:21.938Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T10:49:21.938Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.938Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.938Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T10:49:21.938Z]   Module: linkgenerator
[2025-06-21T10:49:21.938Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T10:49:21.938Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.938Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.938Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:49:21.939Z] Suite Duration: 1606ms
[2025-06-21T10:49:21.939Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:49:21.955Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.956Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T10:49:21.956Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.956Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.956Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.956Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T10:49:21.956Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.956Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.956Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.956Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T10:49:21.956Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.956Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.956Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.956Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T10:49:21.956Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.956Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.956Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.956Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T10:49:21.956Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.956Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.956Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.956Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T10:49:21.956Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.956Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.956Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T10:49:21.956Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.957Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T10:49:21.957Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.957Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.957Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T10:49:21.957Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.957Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T10:49:21.957Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.957Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.957Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T10:49:21.957Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.957Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T10:49:21.957Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.957Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.957Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T10:49:21.957Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.957Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T10:49:21.957Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.957Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.957Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T10:49:21.957Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.957Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T10:49:21.957Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.957Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.957Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T10:49:21.957Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.957Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T10:49:21.958Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.958Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.958Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T10:49:21.958Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.958Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T10:49:21.958Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.958Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.958Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T10:49:21.958Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.958Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T10:49:21.958Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.958Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.958Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T10:49:21.958Z]   Module: sessionidgenerator
[2025-06-21T10:49:21.958Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T10:49:21.958Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:21.958Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:21.958Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:49:21.958Z] Suite Duration: 1687ms
[2025-06-21T10:49:21.958Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T10:49:22.123Z]   [Arrange] - Precondition: Test environment prepared for "should export a service instance"
[2025-06-21T10:49:22.123Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T10:49:22.123Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.124Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.124Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.124Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.124Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.124Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.124Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.124Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.124Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.124Z]   [CASE END] - Duration: 250ms
[2025-06-21T10:49:22.124Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T10:49:22.124Z]   Module: whatsapp
[2025-06-21T10:49:22.124Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T10:49:22.196Z]   [Arrange] - Precondition: Test environment prepared for "should have required methods"
[2025-06-21T10:49:22.196Z]   [Act] - Step: Executing test logic for "should have required methods"
[2025-06-21T10:49:22.196Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.196Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.196Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.196Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.196Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.196Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.196Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.196Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.196Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.196Z]   [CASE END] - Duration: 73ms
[2025-06-21T10:49:22.196Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T10:49:22.197Z]   Module: whatsapp
[2025-06-21T10:49:22.197Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T10:49:22.267Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T10:49:22.267Z]   Module: whatsapp
[2025-06-21T10:49:22.267Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T10:49:22.267Z]   [Arrange] - Precondition: Test environment prepared for "should have configuration properties or be configurable"
[2025-06-21T10:49:22.267Z]   [Act] - Step: Executing test logic for "should have configuration properties or be configurable"
[2025-06-21T10:49:22.267Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.267Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.267Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.267Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.267Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.267Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.267Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.267Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.267Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.268Z]   [CASE END] - Duration: 69ms
[2025-06-21T10:49:22.349Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T10:49:22.349Z]   Module: whatsapp
[2025-06-21T10:49:22.350Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T10:49:22.403Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T10:49:22.403Z]   Module: order
[2025-06-21T10:49:22.404Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T10:49:22.404Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.404Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.404Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T10:49:22.404Z]   Module: order
[2025-06-21T10:49:22.404Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T10:49:22.404Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.404Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.404Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T10:49:22.404Z]   Module: order
[2025-06-21T10:49:22.404Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T10:49:22.404Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.404Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.404Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T10:49:22.404Z]   Module: order
[2025-06-21T10:49:22.404Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T10:49:22.404Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.404Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.404Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:49:22.405Z] Suite Duration: 2100ms
[2025-06-21T10:49:22.405Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:49:22.417Z]   [Arrange] - Precondition: Test environment prepared for "should have retry configuration or error handling"
[2025-06-21T10:49:22.417Z]   [Act] - Step: Executing test logic for "should have retry configuration or error handling"
[2025-06-21T10:49:22.417Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.417Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.417Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.417Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.417Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.417Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.418Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.418Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.418Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.418Z]   [CASE END] - Duration: 68ms
[2025-06-21T10:49:22.418Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T10:49:22.418Z]   Module: whatsapp
[2025-06-21T10:49:22.418Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T10:49:22.449Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:49:22.449Z]   Module: order
[2025-06-21T10:49:22.449Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:49:22.450Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.450Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.450Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:49:22.450Z]   Module: order
[2025-06-21T10:49:22.450Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:49:22.450Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.450Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.450Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:49:22.450Z]   Module: order
[2025-06-21T10:49:22.450Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:49:22.450Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.450Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.450Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:49:22.450Z] Suite Duration: 2164ms
[2025-06-21T10:49:22.450Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:49:22.471Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T10:49:22.471Z]   Module: order
[2025-06-21T10:49:22.471Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T10:49:22.471Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.471Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.471Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T10:49:22.471Z]   Module: order
[2025-06-21T10:49:22.471Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T10:49:22.471Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.471Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.471Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T10:49:22.471Z]   Module: order
[2025-06-21T10:49:22.471Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T10:49:22.471Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.471Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.471Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T10:49:22.471Z]   Module: order
[2025-06-21T10:49:22.471Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T10:49:22.471Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.471Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.472Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:49:22.472Z]   Module: order
[2025-06-21T10:49:22.472Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:49:22.472Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.472Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.472Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:49:22.472Z] Suite Duration: 2209ms
[2025-06-21T10:49:22.472Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:49:22.602Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T10:49:22.602Z]   Module: order
[2025-06-21T10:49:22.602Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T10:49:22.603Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.603Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.603Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T10:49:22.603Z]   Module: order
[2025-06-21T10:49:22.603Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T10:49:22.603Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.603Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.603Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T10:49:22.603Z]   Module: order
[2025-06-21T10:49:22.603Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T10:49:22.603Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.603Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.603Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T10:49:22.603Z]   Module: order
[2025-06-21T10:49:22.603Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T10:49:22.603Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.603Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.603Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T10:49:22.603Z]   Module: order
[2025-06-21T10:49:22.603Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T10:49:22.603Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.603Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.603Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:49:22.603Z] Suite Duration: 2310ms
[2025-06-21T10:49:22.603Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:49:22.641Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T10:49:22.642Z]   Module: session
[2025-06-21T10:49:22.642Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T10:49:22.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.642Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T10:49:22.642Z]   Module: session
[2025-06-21T10:49:22.642Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T10:49:22.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.642Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T10:49:22.642Z]   Module: session
[2025-06-21T10:49:22.642Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T10:49:22.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.642Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T10:49:22.642Z]   Module: session
[2025-06-21T10:49:22.642Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T10:49:22.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.642Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T10:49:22.642Z]   Module: session
[2025-06-21T10:49:22.642Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T10:49:22.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.642Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T10:49:22.642Z]   Module: session
[2025-06-21T10:49:22.642Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T10:49:22.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.642Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:49:22.642Z] Suite Duration: 2365ms
[2025-06-21T10:49:22.642Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:49:22.666Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call getAccessToken method"
[2025-06-21T10:49:22.666Z]   [Act] - Step: Executing test logic for "should be able to call getAccessToken method"
[2025-06-21T10:49:22.666Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.666Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.666Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.666Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.666Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.666Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.666Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.666Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.666Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.667Z]   [CASE END] - Duration: 249ms
[2025-06-21T10:49:22.667Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T10:49:22.667Z]   Module: whatsapp
[2025-06-21T10:49:22.667Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T10:49:22.706Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call sendBasicText method"
[2025-06-21T10:49:22.706Z]   [Act] - Step: Executing test logic for "should be able to call sendBasicText method"
[2025-06-21T10:49:22.707Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.707Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.707Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.707Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.707Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.707Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.707Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.707Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.707Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.707Z]   [CASE END] - Duration: 40ms
[2025-06-21T10:49:22.707Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T10:49:22.707Z]   Module: whatsapp
[2025-06-21T10:49:22.707Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T10:49:22.736Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call sendQuickReply method"
[2025-06-21T10:49:22.736Z]   [Act] - Step: Executing test logic for "should be able to call sendQuickReply method"
[2025-06-21T10:49:22.736Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:22.736Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:22.736Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:22.736Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:22.736Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:22.736Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:22.736Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:22.736Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:22.736Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:22.736Z]   [CASE END] - Duration: 30ms
[2025-06-21T10:49:22.739Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:49:22.739Z] Suite Duration: 2446ms
[2025-06-21T10:49:22.739Z] Suite Results: 8 passed, 0 failed, 0 skipped
[2025-06-21T10:49:22.982Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T10:49:22.983Z]   Module: dialog
[2025-06-21T10:49:22.983Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T10:49:22.983Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.983Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.983Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T10:49:22.983Z]   Module: dialog
[2025-06-21T10:49:22.983Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T10:49:22.983Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.983Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.983Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T10:49:22.983Z]   Module: dialog
[2025-06-21T10:49:22.983Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T10:49:22.983Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.983Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.983Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T10:49:22.983Z]   Module: dialog
[2025-06-21T10:49:22.983Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T10:49:22.983Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.983Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.983Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T10:49:22.983Z]   Module: dialog
[2025-06-21T10:49:22.983Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T10:49:22.983Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.983Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.983Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T10:49:22.984Z]   Module: dialog
[2025-06-21T10:49:22.984Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T10:49:22.984Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:22.984Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:22.984Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:49:22.984Z] Suite Duration: 2691ms
[2025-06-21T10:49:22.984Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:49:23.054Z] 
================================================================================
[2025-06-21T10:49:23.054Z] Test Run Finished
[2025-06-21T10:49:23.054Z] End Timestamp: 2025-06-21T10:49:23.054Z
[2025-06-21T10:49:23.054Z] Total Duration: 3087ms (3.09s)
[2025-06-21T10:49:23.054Z] 
[STATISTICS]
[2025-06-21T10:49:23.054Z] Total Test Suites: 12
[2025-06-21T10:49:23.054Z] Passed Test Suites: 1
[2025-06-21T10:49:23.054Z] Failed Test Suites: 0
[2025-06-21T10:49:23.054Z] Total Tests: 94
[2025-06-21T10:49:23.054Z] Passed Tests: 8
[2025-06-21T10:49:23.054Z] Failed Tests: 0
[2025-06-21T10:49:23.054Z] Skipped Tests: 86
[2025-06-21T10:49:23.054Z] Success Rate: 8.51%
[2025-06-21T10:49:23.054Z] Overall Result: FAILURE
[2025-06-21T10:49:23.054Z] ================================================================================
