[2025-06-21T11:07:43.649Z] ================================================================================
[2025-06-21T11:07:43.649Z] Test Run Started
[2025-06-21T11:07:43.649Z] Timestamp: 2025-06-21T11:07:43.648Z
[2025-06-21T11:07:43.649Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:07:43.649Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:07:43.649Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:07:43.649Z] ================================================================================
[2025-06-21T11:07:43.654Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:07:43.654Z] Total Test Suites: 12
[2025-06-21T11:07:43.654Z] Test Environment: test
[2025-06-21T11:07:43.751Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:07:43.751Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.751Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:07:43.751Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.751Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:07:43.751Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.751Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:07:43.751Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.751Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:07:43.751Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.751Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.752Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.752Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.752Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.752Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.752Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:43.752Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:07:43.752Z] Suite Display Name: UNIT
[2025-06-21T11:07:45.480Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T11:07:45.481Z]   Module: restaurantstore
[2025-06-21T11:07:45.481Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T11:07:45.482Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.482Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.482Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T11:07:45.482Z]   Module: restaurantstore
[2025-06-21T11:07:45.482Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T11:07:45.482Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.482Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.482Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T11:07:45.482Z]   Module: restaurantstore
[2025-06-21T11:07:45.482Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T11:07:45.482Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.483Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.483Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T11:07:45.483Z]   Module: restaurantstore
[2025-06-21T11:07:45.483Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T11:07:45.483Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.483Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.483Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T11:07:45.483Z]   Module: restaurantstore
[2025-06-21T11:07:45.483Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T11:07:45.483Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.483Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.483Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T11:07:45.483Z]   Module: restaurantstore
[2025-06-21T11:07:45.483Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T11:07:45.483Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.483Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.483Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T11:07:45.483Z]   Module: restaurantstore
[2025-06-21T11:07:45.483Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T11:07:45.484Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.484Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.484Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T11:07:45.484Z]   Module: restaurantstore
[2025-06-21T11:07:45.484Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T11:07:45.484Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.484Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.484Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T11:07:45.484Z]   Module: restaurantstore
[2025-06-21T11:07:45.484Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T11:07:45.484Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.484Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.484Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:07:45.484Z] Suite Duration: 1481ms
[2025-06-21T11:07:45.484Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:07:45.491Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T11:07:45.491Z]   Module: sessionservice
[2025-06-21T11:07:45.491Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T11:07:45.491Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.491Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.491Z] 
  [CASE START] - ***********: should have required methods
[2025-06-21T11:07:45.491Z]   Module: sessionservice
[2025-06-21T11:07:45.492Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T11:07:45.492Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.492Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.492Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T11:07:45.492Z]   Module: sessionservice
[2025-06-21T11:07:45.492Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T11:07:45.492Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.492Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.492Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T11:07:45.492Z]   Module: sessionservice
[2025-06-21T11:07:45.492Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T11:07:45.492Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.492Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.492Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T11:07:45.492Z]   Module: sessionservice
[2025-06-21T11:07:45.492Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T11:07:45.492Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.492Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.492Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T11:07:45.492Z]   Module: sessionservice
[2025-06-21T11:07:45.492Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T11:07:45.492Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.492Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.492Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T11:07:45.493Z]   Module: sessionservice
[2025-06-21T11:07:45.493Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T11:07:45.493Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.493Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.493Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T11:07:45.493Z]   Module: sessionservice
[2025-06-21T11:07:45.493Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T11:07:45.493Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.493Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.493Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T11:07:45.493Z]   Module: sessionservice
[2025-06-21T11:07:45.493Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T11:07:45.493Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.493Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.493Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T11:07:45.493Z]   Module: sessionservice
[2025-06-21T11:07:45.493Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T11:07:45.493Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.493Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.493Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T11:07:45.494Z]   Module: sessionservice
[2025-06-21T11:07:45.494Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T11:07:45.494Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.494Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.494Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:07:45.494Z] Suite Duration: 1485ms
[2025-06-21T11:07:45.494Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T11:07:45.613Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T11:07:45.613Z]   Module: whatsapp
[2025-06-21T11:07:45.613Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T11:07:45.617Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T11:07:45.617Z]   Module: linkgenerator
[2025-06-21T11:07:45.617Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T11:07:45.617Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.617Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.617Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T11:07:45.618Z]   Module: linkgenerator
[2025-06-21T11:07:45.618Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T11:07:45.618Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.618Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.618Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T11:07:45.618Z]   Module: linkgenerator
[2025-06-21T11:07:45.618Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T11:07:45.618Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.618Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.618Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T11:07:45.618Z]   Module: linkgenerator
[2025-06-21T11:07:45.618Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T11:07:45.618Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.618Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.618Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T11:07:45.618Z]   Module: linkgenerator
[2025-06-21T11:07:45.618Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T11:07:45.618Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.619Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.619Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T11:07:45.619Z]   Module: linkgenerator
[2025-06-21T11:07:45.619Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T11:07:45.619Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.619Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.619Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:07:45.619Z] Suite Duration: 1539ms
[2025-06-21T11:07:45.619Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:07:45.676Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T11:07:45.676Z]   Module: messagebuilders
[2025-06-21T11:07:45.676Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T11:07:45.676Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.676Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.676Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T11:07:45.676Z]   Module: messagebuilders
[2025-06-21T11:07:45.677Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T11:07:45.677Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.677Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.677Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T11:07:45.677Z]   Module: messagebuilders
[2025-06-21T11:07:45.677Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T11:07:45.677Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.677Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.677Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T11:07:45.677Z]   Module: messagebuilders
[2025-06-21T11:07:45.677Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T11:07:45.677Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.677Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.677Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T11:07:45.677Z]   Module: messagebuilders
[2025-06-21T11:07:45.677Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T11:07:45.677Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.677Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.678Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T11:07:45.678Z]   Module: messagebuilders
[2025-06-21T11:07:45.678Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T11:07:45.678Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.678Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.678Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T11:07:45.678Z]   Module: messagebuilders
[2025-06-21T11:07:45.678Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T11:07:45.678Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.678Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.678Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T11:07:45.678Z]   Module: messagebuilders
[2025-06-21T11:07:45.678Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T11:07:45.678Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.678Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.678Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T11:07:45.679Z]   Module: messagebuilders
[2025-06-21T11:07:45.679Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T11:07:45.679Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.679Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.679Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T11:07:45.679Z]   Module: messagebuilders
[2025-06-21T11:07:45.679Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T11:07:45.679Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.679Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.679Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T11:07:45.679Z]   Module: messagebuilders
[2025-06-21T11:07:45.679Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T11:07:45.679Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.679Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.679Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T11:07:45.679Z]   Module: messagebuilders
[2025-06-21T11:07:45.680Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T11:07:45.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.680Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T11:07:45.680Z]   Module: messagebuilders
[2025-06-21T11:07:45.680Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T11:07:45.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.680Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T11:07:45.680Z]   Module: messagebuilders
[2025-06-21T11:07:45.680Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T11:07:45.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.680Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T11:07:45.680Z]   Module: messagebuilders
[2025-06-21T11:07:45.680Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T11:07:45.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.681Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T11:07:45.681Z]   Module: messagebuilders
[2025-06-21T11:07:45.681Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T11:07:45.681Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.681Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.681Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:07:45.681Z] Suite Duration: 1703ms
[2025-06-21T11:07:45.681Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T11:07:45.766Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T11:07:45.766Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.766Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T11:07:45.766Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.766Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.766Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T11:07:45.766Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.766Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T11:07:45.766Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.766Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.766Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T11:07:45.766Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.766Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T11:07:45.766Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T11:07:45.767Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.767Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T11:07:45.767Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T11:07:45.767Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.767Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T11:07:45.767Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T11:07:45.767Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.767Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T11:07:45.767Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T11:07:45.767Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.767Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T11:07:45.767Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T11:07:45.767Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.767Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T11:07:45.767Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T11:07:45.767Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.767Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T11:07:45.767Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.767Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.767Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T11:07:45.768Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.768Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T11:07:45.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.768Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T11:07:45.768Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.768Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T11:07:45.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.768Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T11:07:45.768Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.768Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T11:07:45.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.768Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T11:07:45.768Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.768Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T11:07:45.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.768Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T11:07:45.768Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.768Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T11:07:45.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.768Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T11:07:45.768Z]   Module: sessionidgenerator
[2025-06-21T11:07:45.768Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T11:07:45.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:45.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:45.768Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:07:45.768Z] Suite Duration: 1774ms
[2025-06-21T11:07:45.768Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T11:07:46.011Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call getAccessToken method"
[2025-06-21T11:07:46.012Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:07:46.012Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:07:46.012Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:07:46.012Z]   [Act] - Step: Executing test logic for "should be able to call getAccessToken method"
[2025-06-21T11:07:46.012Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:07:46.012Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:07:46.012Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:07:46.012Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:07:46.012Z]   [Act Log] - Captured 6 program outputs during execution
[2025-06-21T11:07:46.012Z]   [Act Log] - console: 2 outputs
[2025-06-21T11:07:46.012Z]   [Act Log] - process: 4 outputs
[2025-06-21T11:07:46.012Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:07:46.012Z]   [Act Log] - +91ms [console.log] Console log level: info
[2025-06-21T11:07:46.012Z]   [Act Log] - +91ms [process.stdout] Console log level: info

[2025-06-21T11:07:46.013Z]   [Act Log] - +103ms [console.log] File log level: warn
[2025-06-21T11:07:46.013Z]   [Act Log] - +103ms [process.stdout] File log level: warn

[2025-06-21T11:07:46.013Z]   [Act Log] - +497ms [process.stdout] 2025-06-21 12:07:46:746 [31merror[39m: [31mAxios Response Error: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "url": "https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
}

[2025-06-21T11:07:46.013Z]   [Act Log] - +500ms [process.stdout] 2025-06-21 12:07:46:746 [31merror[39m: [31mError getting access token: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "stack": "Error: getaddrinfo ENOTFOUND test-auth.example.com\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)",
  "request": {
    "url": "https://test-auth.example.com/token",
    "method": "POST",
    "params": "client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
  }
}

[2025-06-21T11:07:46.013Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:07:46.013Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:07:46.013Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:07:46.013Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:07:46.013Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:07:46.013Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:07:46.013Z]   [Assert Log] - Expected: Method "getAccessToken" should be callable without throwing
[2025-06-21T11:07:46.014Z]   [Assert Log] - Actual: service.getAccessToken() executed
[2025-06-21T11:07:46.014Z]   [Assert Log] - Comparison: expect(() => service.getAccessToken()).not.toThrow() - FAILED
[2025-06-21T11:07:46.014Z]   [Assert Log] - Error details: 2025-06-21 12:07:46:746 [31merror[39m: [31mAxios Response Error: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "url": "https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
}

[2025-06-21T11:07:46.014Z]   [Assert Log] - Variable: error.code = "ENOTFOUND"
[2025-06-21T11:07:46.014Z]   [Assert Log] - Variable: error.hostname = "test-auth.example.com"
[2025-06-21T11:07:46.014Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:07:46.014Z]   [CASE END] - Duration: 511ms
[2025-06-21T11:07:46.018Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T11:07:46.018Z]   Module: whatsapp
[2025-06-21T11:07:46.018Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T11:07:46.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.018Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T11:07:46.018Z]   Module: whatsapp
[2025-06-21T11:07:46.018Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T11:07:46.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.018Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T11:07:46.018Z]   Module: whatsapp
[2025-06-21T11:07:46.018Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T11:07:46.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.018Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T11:07:46.018Z]   Module: whatsapp
[2025-06-21T11:07:46.018Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T11:07:46.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.018Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T11:07:46.018Z]   Module: whatsapp
[2025-06-21T11:07:46.019Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T11:07:46.019Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.019Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.019Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T11:07:46.019Z]   Module: whatsapp
[2025-06-21T11:07:46.019Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T11:07:46.019Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.019Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.019Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T11:07:46.019Z]   Module: whatsapp
[2025-06-21T11:07:46.019Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T11:07:46.019Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.019Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.019Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:07:46.019Z] Suite Duration: 1967ms
[2025-06-21T11:07:46.019Z] Suite Results: 1 passed, 0 failed, 7 skipped
[2025-06-21T11:07:46.112Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:07:46.112Z]   Module: order
[2025-06-21T11:07:46.112Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:07:46.112Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.112Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.113Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:07:46.113Z]   Module: order
[2025-06-21T11:07:46.113Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:07:46.113Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.113Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.113Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:07:46.113Z]   Module: order
[2025-06-21T11:07:46.113Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:07:46.113Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.113Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.113Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:07:46.113Z] Suite Duration: 2133ms
[2025-06-21T11:07:46.113Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:07:46.179Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T11:07:46.179Z]   Module: order
[2025-06-21T11:07:46.179Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T11:07:46.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.179Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T11:07:46.179Z]   Module: order
[2025-06-21T11:07:46.179Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T11:07:46.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.179Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T11:07:46.179Z]   Module: order
[2025-06-21T11:07:46.179Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T11:07:46.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.179Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T11:07:46.179Z]   Module: order
[2025-06-21T11:07:46.179Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T11:07:46.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.179Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:07:46.179Z] Suite Duration: 2203ms
[2025-06-21T11:07:46.179Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:07:46.198Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T11:07:46.198Z]   Module: order
[2025-06-21T11:07:46.198Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T11:07:46.198Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.198Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.198Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T11:07:46.198Z]   Module: order
[2025-06-21T11:07:46.198Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T11:07:46.198Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.198Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.198Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T11:07:46.199Z]   Module: order
[2025-06-21T11:07:46.199Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T11:07:46.199Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.199Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.199Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T11:07:46.199Z]   Module: order
[2025-06-21T11:07:46.199Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T11:07:46.199Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.199Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.199Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:07:46.199Z]   Module: order
[2025-06-21T11:07:46.199Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:07:46.199Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.199Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.199Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:07:46.199Z] Suite Duration: 2222ms
[2025-06-21T11:07:46.199Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:07:46.316Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T11:07:46.316Z]   Module: order
[2025-06-21T11:07:46.316Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T11:07:46.316Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.316Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.316Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T11:07:46.316Z]   Module: order
[2025-06-21T11:07:46.316Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T11:07:46.316Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.316Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.316Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T11:07:46.316Z]   Module: order
[2025-06-21T11:07:46.316Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T11:07:46.316Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.316Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.316Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T11:07:46.316Z]   Module: order
[2025-06-21T11:07:46.316Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T11:07:46.316Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.316Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.316Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T11:07:46.317Z]   Module: order
[2025-06-21T11:07:46.317Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T11:07:46.317Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.317Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.317Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:07:46.317Z] Suite Duration: 2335ms
[2025-06-21T11:07:46.317Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:07:46.364Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T11:07:46.364Z]   Module: session
[2025-06-21T11:07:46.364Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T11:07:46.364Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.364Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.364Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T11:07:46.364Z]   Module: session
[2025-06-21T11:07:46.364Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T11:07:46.364Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.364Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.364Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T11:07:46.364Z]   Module: session
[2025-06-21T11:07:46.364Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T11:07:46.364Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.364Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.364Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T11:07:46.364Z]   Module: session
[2025-06-21T11:07:46.364Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T11:07:46.364Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.364Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.364Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T11:07:46.364Z]   Module: session
[2025-06-21T11:07:46.364Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T11:07:46.364Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.364Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.364Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T11:07:46.364Z]   Module: session
[2025-06-21T11:07:46.364Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T11:07:46.365Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.365Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.365Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:07:46.365Z] Suite Duration: 2364ms
[2025-06-21T11:07:46.365Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:07:46.679Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T11:07:46.679Z]   Module: dialog
[2025-06-21T11:07:46.679Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T11:07:46.679Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.679Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.679Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T11:07:46.680Z]   Module: dialog
[2025-06-21T11:07:46.680Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T11:07:46.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.680Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T11:07:46.680Z]   Module: dialog
[2025-06-21T11:07:46.680Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T11:07:46.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.680Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T11:07:46.680Z]   Module: dialog
[2025-06-21T11:07:46.680Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T11:07:46.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.680Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T11:07:46.680Z]   Module: dialog
[2025-06-21T11:07:46.680Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T11:07:46.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.680Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T11:07:46.680Z]   Module: dialog
[2025-06-21T11:07:46.680Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T11:07:46.680Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:07:46.680Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:07:46.680Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:07:46.680Z] Suite Duration: 2710ms
[2025-06-21T11:07:46.680Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:07:46.769Z] 
================================================================================
[2025-06-21T11:07:46.769Z] Test Run Finished
[2025-06-21T11:07:46.769Z] End Timestamp: 2025-06-21T11:07:46.769Z
[2025-06-21T11:07:46.769Z] Total Duration: 3121ms (3.12s)
[2025-06-21T11:07:46.769Z] 
[STATISTICS]
[2025-06-21T11:07:46.769Z] Total Test Suites: 12
[2025-06-21T11:07:46.769Z] Passed Test Suites: 1
[2025-06-21T11:07:46.769Z] Failed Test Suites: 0
[2025-06-21T11:07:46.769Z] Total Tests: 94
[2025-06-21T11:07:46.769Z] Passed Tests: 1
[2025-06-21T11:07:46.769Z] Failed Tests: 0
[2025-06-21T11:07:46.769Z] Skipped Tests: 93
[2025-06-21T11:07:46.770Z] Success Rate: 1.06%
[2025-06-21T11:07:46.770Z] Overall Result: FAILURE
[2025-06-21T11:07:46.770Z] ================================================================================
