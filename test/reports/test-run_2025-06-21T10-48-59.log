[2025-06-21T10:48:59.416Z] ================================================================================
[2025-06-21T10:48:59.416Z] Test Run Started
[2025-06-21T10:48:59.416Z] Timestamp: 2025-06-21T10:48:59.416Z
[2025-06-21T10:48:59.416Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:48:59.416Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:48:59.416Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:48:59.416Z] ================================================================================
[2025-06-21T10:48:59.422Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:48:59.422Z] Total Test Suites: 12
[2025-06-21T10:48:59.422Z] Test Environment: test
[2025-06-21T10:48:59.509Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:48:59.509Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:48:59.510Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:48:59.510Z] Suite Display Name: UNIT
[2025-06-21T10:49:01.124Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T10:49:01.124Z]   Module: sessionservice
[2025-06-21T10:49:01.124Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T10:49:01.124Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.124Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.124Z] 
  [CASE START] - TC-55708206: should have required methods
[2025-06-21T10:49:01.124Z]   Module: sessionservice
[2025-06-21T10:49:01.124Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T10:49:01.124Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.124Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.124Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T10:49:01.125Z]   Module: sessionservice
[2025-06-21T10:49:01.125Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T10:49:01.125Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.125Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.125Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T10:49:01.125Z]   Module: sessionservice
[2025-06-21T10:49:01.125Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T10:49:01.125Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.125Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.125Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T10:49:01.125Z]   Module: sessionservice
[2025-06-21T10:49:01.125Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T10:49:01.125Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.125Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.125Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T10:49:01.126Z]   Module: sessionservice
[2025-06-21T10:49:01.126Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T10:49:01.126Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.126Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.126Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T10:49:01.126Z]   Module: sessionservice
[2025-06-21T10:49:01.126Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T10:49:01.126Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.126Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.126Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T10:49:01.126Z]   Module: sessionservice
[2025-06-21T10:49:01.126Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T10:49:01.126Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.126Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.126Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T10:49:01.126Z]   Module: sessionservice
[2025-06-21T10:49:01.126Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T10:49:01.126Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.126Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.127Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T10:49:01.127Z]   Module: sessionservice
[2025-06-21T10:49:01.127Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T10:49:01.127Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.127Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.127Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T10:49:01.127Z]   Module: sessionservice
[2025-06-21T10:49:01.127Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T10:49:01.127Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.127Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.127Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:49:01.127Z] Suite Duration: 1381ms
[2025-06-21T10:49:01.127Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T10:49:01.128Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T10:49:01.128Z]   Module: restaurantstore
[2025-06-21T10:49:01.128Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T10:49:01.128Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.128Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.128Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T10:49:01.128Z]   Module: restaurantstore
[2025-06-21T10:49:01.128Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T10:49:01.128Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.129Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.129Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T10:49:01.129Z]   Module: restaurantstore
[2025-06-21T10:49:01.129Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T10:49:01.129Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.129Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.129Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T10:49:01.129Z]   Module: restaurantstore
[2025-06-21T10:49:01.129Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T10:49:01.129Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.129Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.129Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T10:49:01.129Z]   Module: restaurantstore
[2025-06-21T10:49:01.129Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T10:49:01.129Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.129Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.129Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T10:49:01.129Z]   Module: restaurantstore
[2025-06-21T10:49:01.129Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T10:49:01.129Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.129Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.129Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T10:49:01.129Z]   Module: restaurantstore
[2025-06-21T10:49:01.129Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T10:49:01.130Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.130Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.130Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T10:49:01.130Z]   Module: restaurantstore
[2025-06-21T10:49:01.130Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T10:49:01.130Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.130Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.130Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T10:49:01.130Z]   Module: restaurantstore
[2025-06-21T10:49:01.130Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T10:49:01.130Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.130Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.130Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:49:01.130Z] Suite Duration: 1394ms
[2025-06-21T10:49:01.130Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:49:01.185Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T10:49:01.185Z]   Module: whatsapp
[2025-06-21T10:49:01.185Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T10:49:01.202Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T10:49:01.202Z]   Module: linkgenerator
[2025-06-21T10:49:01.202Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T10:49:01.202Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.202Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.202Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T10:49:01.202Z]   Module: linkgenerator
[2025-06-21T10:49:01.202Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T10:49:01.202Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.202Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.202Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T10:49:01.202Z]   Module: linkgenerator
[2025-06-21T10:49:01.202Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T10:49:01.202Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.202Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.203Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T10:49:01.203Z]   Module: linkgenerator
[2025-06-21T10:49:01.203Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T10:49:01.203Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.203Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.203Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T10:49:01.203Z]   Module: linkgenerator
[2025-06-21T10:49:01.203Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T10:49:01.203Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.203Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.203Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T10:49:01.203Z]   Module: linkgenerator
[2025-06-21T10:49:01.203Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T10:49:01.203Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.207Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.207Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:49:01.207Z] Suite Duration: 1433ms
[2025-06-21T10:49:01.207Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:49:01.212Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T10:49:01.212Z]   Module: messagebuilders
[2025-06-21T10:49:01.212Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T10:49:01.212Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.212Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.212Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T10:49:01.212Z]   Module: messagebuilders
[2025-06-21T10:49:01.212Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T10:49:01.212Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.212Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.212Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T10:49:01.212Z]   Module: messagebuilders
[2025-06-21T10:49:01.212Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T10:49:01.212Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.212Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.212Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T10:49:01.213Z]   Module: messagebuilders
[2025-06-21T10:49:01.213Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T10:49:01.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.213Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T10:49:01.213Z]   Module: messagebuilders
[2025-06-21T10:49:01.213Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T10:49:01.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.213Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T10:49:01.213Z]   Module: messagebuilders
[2025-06-21T10:49:01.213Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T10:49:01.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.213Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T10:49:01.213Z]   Module: messagebuilders
[2025-06-21T10:49:01.213Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T10:49:01.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.213Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T10:49:01.214Z]   Module: messagebuilders
[2025-06-21T10:49:01.214Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T10:49:01.214Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.214Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.214Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T10:49:01.214Z]   Module: messagebuilders
[2025-06-21T10:49:01.214Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T10:49:01.214Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.214Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.214Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T10:49:01.214Z]   Module: messagebuilders
[2025-06-21T10:49:01.214Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T10:49:01.214Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.214Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.214Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T10:49:01.215Z]   Module: messagebuilders
[2025-06-21T10:49:01.215Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T10:49:01.215Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.215Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.215Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T10:49:01.215Z]   Module: messagebuilders
[2025-06-21T10:49:01.215Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T10:49:01.215Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.215Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.215Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T10:49:01.215Z]   Module: messagebuilders
[2025-06-21T10:49:01.215Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T10:49:01.215Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.215Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.215Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T10:49:01.215Z]   Module: messagebuilders
[2025-06-21T10:49:01.215Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T10:49:01.215Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.216Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.216Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T10:49:01.216Z]   Module: messagebuilders
[2025-06-21T10:49:01.216Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T10:49:01.216Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.216Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.216Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T10:49:01.216Z]   Module: messagebuilders
[2025-06-21T10:49:01.216Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T10:49:01.216Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.216Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.216Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:49:01.216Z] Suite Duration: 1507ms
[2025-06-21T10:49:01.216Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T10:49:01.227Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T10:49:01.227Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.227Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T10:49:01.227Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.227Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.227Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T10:49:01.227Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T10:49:01.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.228Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T10:49:01.228Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T10:49:01.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.228Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T10:49:01.228Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T10:49:01.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.228Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T10:49:01.228Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T10:49:01.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.228Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T10:49:01.228Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.228Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T10:49:01.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.229Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T10:49:01.229Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.229Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T10:49:01.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.229Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T10:49:01.229Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.229Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T10:49:01.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.229Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T10:49:01.229Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.229Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T10:49:01.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.229Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T10:49:01.229Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.229Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T10:49:01.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.229Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T10:49:01.229Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.229Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T10:49:01.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.230Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T10:49:01.230Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.230Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T10:49:01.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.230Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T10:49:01.230Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.230Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T10:49:01.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.230Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T10:49:01.230Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.230Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T10:49:01.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.230Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T10:49:01.230Z]   Module: sessionidgenerator
[2025-06-21T10:49:01.230Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T10:49:01.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.230Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:49:01.230Z] Suite Duration: 1504ms
[2025-06-21T10:49:01.230Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T10:49:01.415Z]   [Arrange] - Precondition: Test environment prepared for "should export a service instance"
[2025-06-21T10:49:01.415Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T10:49:01.415Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:01.415Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:01.415Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:01.415Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:01.415Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:01.415Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:01.415Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:01.415Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:01.415Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:01.415Z]   [CASE END] - Duration: 238ms
[2025-06-21T10:49:01.424Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T10:49:01.424Z]   Module: whatsapp
[2025-06-21T10:49:01.425Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T10:49:01.425Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.425Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.425Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T10:49:01.425Z]   Module: whatsapp
[2025-06-21T10:49:01.425Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T10:49:01.425Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.425Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.425Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T10:49:01.425Z]   Module: whatsapp
[2025-06-21T10:49:01.425Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T10:49:01.425Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.425Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.425Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T10:49:01.425Z]   Module: whatsapp
[2025-06-21T10:49:01.425Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T10:49:01.425Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.425Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.425Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T10:49:01.425Z]   Module: whatsapp
[2025-06-21T10:49:01.425Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T10:49:01.425Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.425Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.425Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T10:49:01.426Z]   Module: whatsapp
[2025-06-21T10:49:01.426Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T10:49:01.426Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.426Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.426Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T10:49:01.426Z]   Module: whatsapp
[2025-06-21T10:49:01.426Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T10:49:01.426Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.426Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.426Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:49:01.426Z] Suite Duration: 1651ms
[2025-06-21T10:49:01.426Z] Suite Results: 1 passed, 0 failed, 7 skipped
[2025-06-21T10:49:01.682Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T10:49:01.683Z]   Module: order
[2025-06-21T10:49:01.683Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T10:49:01.683Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.683Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.683Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T10:49:01.683Z]   Module: order
[2025-06-21T10:49:01.683Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T10:49:01.683Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.683Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.683Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T10:49:01.683Z]   Module: order
[2025-06-21T10:49:01.683Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T10:49:01.683Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.683Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.683Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T10:49:01.683Z]   Module: order
[2025-06-21T10:49:01.683Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T10:49:01.683Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.684Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.684Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:49:01.684Z] Suite Duration: 1974ms
[2025-06-21T10:49:01.684Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:49:01.709Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T10:49:01.709Z]   Module: order
[2025-06-21T10:49:01.709Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T10:49:01.709Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.710Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.710Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T10:49:01.710Z]   Module: order
[2025-06-21T10:49:01.710Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T10:49:01.710Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.710Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.710Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T10:49:01.710Z]   Module: order
[2025-06-21T10:49:01.710Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T10:49:01.710Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.710Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.710Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T10:49:01.710Z]   Module: order
[2025-06-21T10:49:01.710Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T10:49:01.710Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.710Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.710Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:49:01.710Z]   Module: order
[2025-06-21T10:49:01.710Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:49:01.710Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.710Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.710Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:49:01.710Z] Suite Duration: 1990ms
[2025-06-21T10:49:01.710Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:49:01.734Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:49:01.735Z]   Module: order
[2025-06-21T10:49:01.735Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:49:01.735Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.735Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.735Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:49:01.735Z]   Module: order
[2025-06-21T10:49:01.735Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:49:01.735Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.735Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.735Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:49:01.735Z]   Module: order
[2025-06-21T10:49:01.735Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:49:01.735Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.735Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.735Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:49:01.735Z] Suite Duration: 2006ms
[2025-06-21T10:49:01.735Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:49:01.827Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T10:49:01.827Z]   Module: order
[2025-06-21T10:49:01.827Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T10:49:01.827Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.827Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.827Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T10:49:01.827Z]   Module: order
[2025-06-21T10:49:01.827Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T10:49:01.827Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.827Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.827Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T10:49:01.827Z]   Module: order
[2025-06-21T10:49:01.827Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T10:49:01.827Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.827Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.827Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T10:49:01.827Z]   Module: order
[2025-06-21T10:49:01.827Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T10:49:01.827Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.827Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.827Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T10:49:01.827Z]   Module: order
[2025-06-21T10:49:01.827Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T10:49:01.827Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.827Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.827Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:49:01.827Z] Suite Duration: 2113ms
[2025-06-21T10:49:01.827Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:49:01.882Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T10:49:01.882Z]   Module: session
[2025-06-21T10:49:01.882Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T10:49:01.882Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.882Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.882Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T10:49:01.882Z]   Module: session
[2025-06-21T10:49:01.882Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T10:49:01.882Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.882Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.882Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T10:49:01.882Z]   Module: session
[2025-06-21T10:49:01.882Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T10:49:01.882Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.882Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.882Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T10:49:01.882Z]   Module: session
[2025-06-21T10:49:01.882Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T10:49:01.882Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.882Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.882Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T10:49:01.882Z]   Module: session
[2025-06-21T10:49:01.882Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T10:49:01.882Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.882Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.882Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T10:49:01.882Z]   Module: session
[2025-06-21T10:49:01.882Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T10:49:01.882Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:01.882Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:01.882Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:49:01.882Z] Suite Duration: 2157ms
[2025-06-21T10:49:01.882Z] Suite Results: 0 passed, 0 failed, 6 skipped
