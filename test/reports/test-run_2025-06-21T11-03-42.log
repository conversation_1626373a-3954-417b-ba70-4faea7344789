[2025-06-21T11:03:42.102Z] ================================================================================
[2025-06-21T11:03:42.102Z] Test Run Started
[2025-06-21T11:03:42.102Z] Timestamp: 2025-06-21T11:03:42.102Z
[2025-06-21T11:03:42.103Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:03:42.103Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:03:42.103Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:03:42.103Z] ================================================================================
[2025-06-21T11:03:42.109Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:03:42.109Z] Total Test Suites: 12
[2025-06-21T11:03:42.110Z] Test Environment: test
[2025-06-21T11:03:42.236Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:03:42.236Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.236Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:03:42.236Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:42.237Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:03:42.237Z] Suite Display Name: UNIT
[2025-06-21T11:03:44.240Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T11:03:44.242Z]   Module: restaurantstore
[2025-06-21T11:03:44.242Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T11:03:44.243Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.243Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.243Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T11:03:44.243Z]   Module: restaurantstore
[2025-06-21T11:03:44.244Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T11:03:44.244Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.244Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.244Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T11:03:44.244Z]   Module: restaurantstore
[2025-06-21T11:03:44.245Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T11:03:44.245Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.245Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.245Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T11:03:44.245Z]   Module: restaurantstore
[2025-06-21T11:03:44.245Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T11:03:44.245Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.245Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.245Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T11:03:44.245Z]   Module: restaurantstore
[2025-06-21T11:03:44.245Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T11:03:44.245Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.245Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.245Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T11:03:44.246Z]   Module: restaurantstore
[2025-06-21T11:03:44.246Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T11:03:44.246Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.246Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.246Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T11:03:44.246Z]   Module: restaurantstore
[2025-06-21T11:03:44.246Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T11:03:44.246Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.246Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.246Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T11:03:44.246Z]   Module: restaurantstore
[2025-06-21T11:03:44.246Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T11:03:44.246Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.246Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.246Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T11:03:44.246Z]   Module: restaurantstore
[2025-06-21T11:03:44.247Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T11:03:44.247Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.247Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.247Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:03:44.247Z] Suite Duration: 1716ms
[2025-06-21T11:03:44.247Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:03:44.269Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T11:03:44.269Z]   Module: sessionservice
[2025-06-21T11:03:44.269Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T11:03:44.269Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.269Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.269Z] 
  [CASE START] - ***********: should have required methods
[2025-06-21T11:03:44.270Z]   Module: sessionservice
[2025-06-21T11:03:44.270Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T11:03:44.270Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.270Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.270Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T11:03:44.270Z]   Module: sessionservice
[2025-06-21T11:03:44.270Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T11:03:44.270Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.270Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.270Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T11:03:44.270Z]   Module: sessionservice
[2025-06-21T11:03:44.270Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T11:03:44.270Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.270Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.270Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T11:03:44.270Z]   Module: sessionservice
[2025-06-21T11:03:44.270Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T11:03:44.270Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.270Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.270Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T11:03:44.270Z]   Module: sessionservice
[2025-06-21T11:03:44.271Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T11:03:44.271Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.271Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.271Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T11:03:44.271Z]   Module: sessionservice
[2025-06-21T11:03:44.271Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T11:03:44.271Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.271Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.271Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T11:03:44.271Z]   Module: sessionservice
[2025-06-21T11:03:44.271Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T11:03:44.271Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.271Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.271Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T11:03:44.271Z]   Module: sessionservice
[2025-06-21T11:03:44.271Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T11:03:44.271Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.271Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.271Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T11:03:44.288Z]   Module: sessionservice
[2025-06-21T11:03:44.288Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T11:03:44.288Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.288Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.288Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T11:03:44.288Z]   Module: sessionservice
[2025-06-21T11:03:44.288Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T11:03:44.288Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.289Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.289Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:03:44.289Z] Suite Duration: 1761ms
[2025-06-21T11:03:44.289Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T11:03:44.413Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T11:03:44.413Z]   Module: whatsapp
[2025-06-21T11:03:44.414Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T11:03:44.419Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T11:03:44.419Z]   Module: linkgenerator
[2025-06-21T11:03:44.420Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T11:03:44.420Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.420Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.420Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T11:03:44.420Z]   Module: linkgenerator
[2025-06-21T11:03:44.420Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T11:03:44.420Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.420Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.420Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T11:03:44.420Z]   Module: linkgenerator
[2025-06-21T11:03:44.420Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T11:03:44.420Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.420Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.420Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T11:03:44.420Z]   Module: linkgenerator
[2025-06-21T11:03:44.420Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T11:03:44.420Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.420Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.420Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T11:03:44.420Z]   Module: linkgenerator
[2025-06-21T11:03:44.420Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T11:03:44.420Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.421Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.421Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T11:03:44.421Z]   Module: linkgenerator
[2025-06-21T11:03:44.421Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T11:03:44.421Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.421Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.421Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:03:44.421Z] Suite Duration: 1866ms
[2025-06-21T11:03:44.421Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:03:44.429Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T11:03:44.430Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.430Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T11:03:44.430Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.430Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.430Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T11:03:44.430Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.430Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T11:03:44.430Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.430Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.430Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T11:03:44.430Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.430Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T11:03:44.430Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.430Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.430Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T11:03:44.430Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.430Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T11:03:44.430Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.430Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.431Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T11:03:44.431Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.431Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T11:03:44.431Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.431Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.431Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T11:03:44.431Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.431Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T11:03:44.431Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.431Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.431Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T11:03:44.431Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.431Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T11:03:44.431Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.431Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.431Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T11:03:44.431Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.431Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T11:03:44.431Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.431Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.431Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T11:03:44.431Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.431Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T11:03:44.432Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.432Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.432Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T11:03:44.432Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.432Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T11:03:44.432Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.432Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.432Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T11:03:44.432Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.432Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T11:03:44.432Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.432Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.432Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T11:03:44.432Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.432Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T11:03:44.432Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.432Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.432Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T11:03:44.433Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.433Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T11:03:44.433Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.433Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.433Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T11:03:44.434Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.434Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T11:03:44.434Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.434Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.434Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T11:03:44.434Z]   Module: sessionidgenerator
[2025-06-21T11:03:44.434Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T11:03:44.434Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.434Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.434Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:03:44.434Z] Suite Duration: 1907ms
[2025-06-21T11:03:44.434Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T11:03:44.445Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T11:03:44.446Z]   Module: messagebuilders
[2025-06-21T11:03:44.446Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T11:03:44.446Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.446Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.446Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T11:03:44.446Z]   Module: messagebuilders
[2025-06-21T11:03:44.446Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T11:03:44.446Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.446Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.446Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T11:03:44.446Z]   Module: messagebuilders
[2025-06-21T11:03:44.446Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T11:03:44.446Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.446Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.446Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T11:03:44.446Z]   Module: messagebuilders
[2025-06-21T11:03:44.446Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T11:03:44.446Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.446Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.446Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T11:03:44.447Z]   Module: messagebuilders
[2025-06-21T11:03:44.447Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T11:03:44.447Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.447Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.447Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T11:03:44.447Z]   Module: messagebuilders
[2025-06-21T11:03:44.447Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T11:03:44.447Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.447Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.447Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T11:03:44.447Z]   Module: messagebuilders
[2025-06-21T11:03:44.447Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T11:03:44.447Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.447Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.447Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T11:03:44.447Z]   Module: messagebuilders
[2025-06-21T11:03:44.447Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T11:03:44.447Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.447Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.448Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T11:03:44.448Z]   Module: messagebuilders
[2025-06-21T11:03:44.448Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T11:03:44.448Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.448Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.448Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T11:03:44.448Z]   Module: messagebuilders
[2025-06-21T11:03:44.448Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T11:03:44.448Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.448Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.448Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T11:03:44.448Z]   Module: messagebuilders
[2025-06-21T11:03:44.448Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T11:03:44.449Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.449Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.449Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T11:03:44.449Z]   Module: messagebuilders
[2025-06-21T11:03:44.449Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T11:03:44.449Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.449Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.449Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T11:03:44.449Z]   Module: messagebuilders
[2025-06-21T11:03:44.449Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T11:03:44.449Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.449Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.449Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T11:03:44.449Z]   Module: messagebuilders
[2025-06-21T11:03:44.449Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T11:03:44.450Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.450Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.450Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T11:03:44.450Z]   Module: messagebuilders
[2025-06-21T11:03:44.450Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T11:03:44.450Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.450Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.450Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T11:03:44.450Z]   Module: messagebuilders
[2025-06-21T11:03:44.450Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T11:03:44.450Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.450Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.450Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:03:44.450Z] Suite Duration: 1939ms
[2025-06-21T11:03:44.450Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T11:03:44.882Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call getAccessToken method"
[2025-06-21T11:03:44.882Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:03:44.882Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:03:44.882Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:03:44.882Z]   [Act] - Step: Executing test logic for "should be able to call getAccessToken method"
[2025-06-21T11:03:44.883Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:03:44.883Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:03:44.883Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:03:44.883Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:03:44.883Z]   [Act Log] - Captured 6 program outputs during execution
[2025-06-21T11:03:44.883Z]   [Act Log] - console: 2 outputs
[2025-06-21T11:03:44.883Z]   [Act Log] - process: 4 outputs
[2025-06-21T11:03:44.883Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:03:44.883Z]   [Act Log] - +108ms [console.log] Console log level: info
[2025-06-21T11:03:44.883Z]   [Act Log] - +108ms [process.stdout] Console log level: info

[2025-06-21T11:03:44.883Z]   [Act Log] - +117ms [console.log] File log level: warn
[2025-06-21T11:03:44.883Z]   [Act Log] - +117ms [process.stdout] File log level: warn

[2025-06-21T11:03:44.883Z]   [Act Log] - +582ms [process.stdout] 2025-06-21 12:03:44:344 [31merror[39m: [31mAxios Response Error: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "url": "https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
}

[2025-06-21T11:03:44.883Z]   [Act Log] - +586ms [process.stdout] 2025-06-21 12:03:44:344 [31merror[39m: [31mError getting access token: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "stack": "Error: getaddrinfo ENOTFOUND test-auth.example.com\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)",
  "request": {
    "url": "https://test-auth.example.com/token",
    "method": "POST",
    "params": "client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
  }
}

[2025-06-21T11:03:44.883Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:03:44.883Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:03:44.883Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:03:44.883Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:03:44.883Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:03:44.884Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:03:44.884Z]   [Assert Log] - Expected result: Function callable (no exceptions)
[2025-06-21T11:03:44.884Z]   [Assert Log] - Actual result: Function executed without exceptions
[2025-06-21T11:03:44.884Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:03:44.884Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:03:44.884Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:03:44.884Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:03:44.884Z]   [CASE END] - Duration: 612ms
[2025-06-21T11:03:44.890Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T11:03:44.890Z]   Module: whatsapp
[2025-06-21T11:03:44.890Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T11:03:44.890Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.890Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.890Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T11:03:44.890Z]   Module: whatsapp
[2025-06-21T11:03:44.890Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T11:03:44.890Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.890Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.890Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T11:03:44.890Z]   Module: whatsapp
[2025-06-21T11:03:44.890Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T11:03:44.890Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.890Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.890Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T11:03:44.890Z]   Module: whatsapp
[2025-06-21T11:03:44.891Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T11:03:44.891Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.891Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.891Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T11:03:44.891Z]   Module: whatsapp
[2025-06-21T11:03:44.891Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T11:03:44.891Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.891Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.891Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T11:03:44.891Z]   Module: whatsapp
[2025-06-21T11:03:44.891Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T11:03:44.891Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.891Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.891Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T11:03:44.891Z]   Module: whatsapp
[2025-06-21T11:03:44.891Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T11:03:44.891Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:44.891Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:44.891Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:03:44.891Z] Suite Duration: 2360ms
[2025-06-21T11:03:44.891Z] Suite Results: 1 passed, 0 failed, 7 skipped
[2025-06-21T11:03:45.017Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T11:03:45.018Z]   Module: order
[2025-06-21T11:03:45.018Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T11:03:45.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.018Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T11:03:45.018Z]   Module: order
[2025-06-21T11:03:45.018Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T11:03:45.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.018Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T11:03:45.018Z]   Module: order
[2025-06-21T11:03:45.018Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T11:03:45.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.018Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T11:03:45.018Z]   Module: order
[2025-06-21T11:03:45.018Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T11:03:45.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.018Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.018Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:03:45.018Z]   Module: order
[2025-06-21T11:03:45.018Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:03:45.018Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.019Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.019Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:03:45.019Z] Suite Duration: 2438ms
[2025-06-21T11:03:45.019Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:03:45.062Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:03:45.062Z]   Module: order
[2025-06-21T11:03:45.062Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:03:45.062Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.062Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.062Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:03:45.063Z]   Module: order
[2025-06-21T11:03:45.063Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:03:45.063Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.063Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.063Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:03:45.063Z]   Module: order
[2025-06-21T11:03:45.063Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:03:45.063Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.063Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.063Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:03:45.063Z] Suite Duration: 2532ms
[2025-06-21T11:03:45.063Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:03:45.088Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T11:03:45.088Z]   Module: order
[2025-06-21T11:03:45.088Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T11:03:45.088Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.088Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.088Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T11:03:45.088Z]   Module: order
[2025-06-21T11:03:45.089Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T11:03:45.089Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.089Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.089Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T11:03:45.089Z]   Module: order
[2025-06-21T11:03:45.089Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T11:03:45.089Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.089Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.089Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T11:03:45.089Z]   Module: order
[2025-06-21T11:03:45.089Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T11:03:45.089Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.089Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.089Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:03:45.089Z] Suite Duration: 2437ms
[2025-06-21T11:03:45.089Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:03:45.217Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T11:03:45.218Z]   Module: order
[2025-06-21T11:03:45.218Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T11:03:45.218Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.218Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.218Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T11:03:45.218Z]   Module: order
[2025-06-21T11:03:45.218Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T11:03:45.218Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.218Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.218Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T11:03:45.218Z]   Module: order
[2025-06-21T11:03:45.218Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T11:03:45.218Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.218Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.219Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T11:03:45.219Z]   Module: order
[2025-06-21T11:03:45.219Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T11:03:45.219Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.219Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.219Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T11:03:45.219Z]   Module: order
[2025-06-21T11:03:45.219Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T11:03:45.219Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.219Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.219Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:03:45.219Z] Suite Duration: 2666ms
[2025-06-21T11:03:45.219Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:03:45.276Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T11:03:45.277Z]   Module: session
[2025-06-21T11:03:45.277Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T11:03:45.277Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.277Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.277Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T11:03:45.277Z]   Module: session
[2025-06-21T11:03:45.277Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T11:03:45.277Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.277Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.277Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T11:03:45.277Z]   Module: session
[2025-06-21T11:03:45.277Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T11:03:45.277Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.277Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.277Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T11:03:45.277Z]   Module: session
[2025-06-21T11:03:45.277Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T11:03:45.277Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.277Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.277Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T11:03:45.277Z]   Module: session
[2025-06-21T11:03:45.277Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T11:03:45.277Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.277Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.277Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T11:03:45.277Z]   Module: session
[2025-06-21T11:03:45.277Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T11:03:45.278Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.278Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.278Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:03:45.278Z] Suite Duration: 2814ms
[2025-06-21T11:03:45.278Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:03:45.727Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T11:03:45.728Z]   Module: dialog
[2025-06-21T11:03:45.728Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T11:03:45.728Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.728Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.728Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T11:03:45.728Z]   Module: dialog
[2025-06-21T11:03:45.728Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T11:03:45.728Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.728Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.728Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T11:03:45.728Z]   Module: dialog
[2025-06-21T11:03:45.728Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T11:03:45.728Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.728Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.728Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T11:03:45.728Z]   Module: dialog
[2025-06-21T11:03:45.728Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T11:03:45.728Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.728Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.728Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T11:03:45.728Z]   Module: dialog
[2025-06-21T11:03:45.728Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T11:03:45.728Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.728Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.728Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T11:03:45.728Z]   Module: dialog
[2025-06-21T11:03:45.728Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T11:03:45.728Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:45.728Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:45.728Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:03:45.728Z] Suite Duration: 3218ms
[2025-06-21T11:03:45.728Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:03:45.809Z] 
================================================================================
[2025-06-21T11:03:45.809Z] Test Run Finished
[2025-06-21T11:03:45.809Z] End Timestamp: 2025-06-21T11:03:45.809Z
[2025-06-21T11:03:45.809Z] Total Duration: 3707ms (3.71s)
[2025-06-21T11:03:45.809Z] 
[STATISTICS]
[2025-06-21T11:03:45.809Z] Total Test Suites: 12
[2025-06-21T11:03:45.809Z] Passed Test Suites: 1
[2025-06-21T11:03:45.809Z] Failed Test Suites: 0
[2025-06-21T11:03:45.809Z] Total Tests: 94
[2025-06-21T11:03:45.809Z] Passed Tests: 1
[2025-06-21T11:03:45.809Z] Failed Tests: 0
[2025-06-21T11:03:45.809Z] Skipped Tests: 93
[2025-06-21T11:03:45.809Z] Success Rate: 1.06%
[2025-06-21T11:03:45.809Z] Overall Result: FAILURE
[2025-06-21T11:03:45.809Z] ================================================================================
