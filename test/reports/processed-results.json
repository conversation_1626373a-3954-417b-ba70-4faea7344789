{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 10, "numPassedTests": 24, "numPendingTestSuites": 4, "numPendingTests": 28, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 14, "numTotalTests": 52, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750504120324, "success": true, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 3, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504122603, "runtime": 2259, "slow": false, "start": 1750504120344}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js", "testResults": [{"ancestorTitles": ["WhatsApp Webhook Integration"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsApp Webhook Integration should process incoming message and create session", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should process incoming message and create session"}, {"ancestorTitles": ["WhatsApp Webhook Integration"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsApp Webhook Integration should reject webhook with invalid signature", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should reject webhook with invalid signature"}, {"ancestorTitles": ["WhatsApp Webhook Integration"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "WhatsApp Webhook Integration should handle malformed webhook payload", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle malformed webhook payload"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 2, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504122744, "runtime": 135, "slow": false, "start": 1750504122609}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js", "testResults": [{"ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should verify refundStatus default value is NONE"}, {"ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should verify refund status enum values"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 9, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504123798, "runtime": 1050, "slow": false, "start": 1750504122748}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js", "testResults": [{"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Payment Intent Flow should create real payment intent with Stripe", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create real payment intent with <PERSON>e"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Payment Intent Flow should retrieve real payment intent status", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should retrieve real payment intent status"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Payment Intent Flow should handle payment with test card", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle payment with test card"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Payment Intent Flow should handle declined card", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle declined card"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Customer Management should create real Stripe customer", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create real Stripe customer"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Customer Management should retrieve real Stripe customer", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should retrieve real Stripe customer"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Webhook Processing"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Real Webhook Processing should process real webhook signature validation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should process real webhook signature validation"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid amount", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle invalid amount"}, {"ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid currency", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle invalid currency"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 4, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504124573, "runtime": 770, "slow": false, "start": 1750504123803}, "skipped": true, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js", "testResults": [{"ancestorTitles": ["Order GraphQL Mutations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL Mutations should create a new order", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create a new order"}, {"ancestorTitles": ["Order GraphQL Mutations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL Mutations should fail to create order with invalid restaurant ID", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should fail to create order with invalid restaurant ID"}, {"ancestorTitles": ["Order GraphQL Mutations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL Mutations should fail to create order without authentication", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should fail to create order without authentication"}, {"ancestorTitles": ["Order GraphQL Mutations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL Mutations should update order status", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should update order status"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 3, "numPendingTests": 3, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504126265, "runtime": 1688, "slow": false, "start": 1750504124577}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js", "testResults": [{"ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"], "duration": 303, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL API Integration Tests Order Schema Types should have Order type in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have Order type in schema"}, {"ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"], "duration": 32, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL API Integration Tests Order Schema Types should have OrderStatus enum in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have OrderStatus enum in schema"}, {"ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"], "duration": 29, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL API Integration Tests Order Input Types should have OrderInput type in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have OrderInput type in schema"}, {"ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL API Integration Tests Order Input Types should validate GraphQL order operations", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should validate GraphQL order operations"}, {"ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL API Integration Tests GraphQL Error Handling should handle malformed order queries", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle malformed order queries"}, {"ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Order GraphQL API Integration Tests GraphQL Error Handling should validate required arguments", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should validate required arguments"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504127654, "runtime": 1385, "slow": false, "start": 1750504126269}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js", "testResults": [{"ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"], "duration": 63, "failureDetails": [], "failureMessages": [], "fullName": "GraphQL Queries Integration Tests GraphQL Schema Introspection should respond to GraphQL introspection query", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should respond to GraphQL introspection query"}, {"ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"], "duration": 28, "failureDetails": [], "failureMessages": [], "fullName": "GraphQL Queries Integration Tests GraphQL Schema Introspection should have Restaurant type in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have Restaurant type in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504128583, "runtime": 925, "slow": false, "start": 1750504127658}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js", "testResults": [{"ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "duration": 55, "failureDetails": [], "failureMessages": [], "fullName": "PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have payment related types in schema"}, {"ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have query types in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have query types in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504129700, "runtime": 1115, "slow": false, "start": 1750504128585}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js", "testResults": [{"ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "duration": 84, "failureDetails": [], "failureMessages": [], "fullName": "Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have payment related types in schema"}, {"ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "duration": 48, "failureDetails": [], "failureMessages": [], "fullName": "Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have mutation types in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have mutation types in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 3, "numPendingTests": 2, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504130760, "runtime": 1053, "slow": false, "start": 1750504129707}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js", "testResults": [{"ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"], "duration": 61, "failureDetails": [], "failureMessages": [], "fullName": "Customer GraphQL API Integration Tests Customer Schema Types should have Customer type in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have Customer type in schema"}, {"ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"], "duration": 34, "failureDetails": [], "failureMessages": [], "fullName": "Customer GraphQL API Integration Tests Customer Schema Types should have Address type in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have Address type in schema"}, {"ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Input Types"], "duration": 35, "failureDetails": [], "failureMessages": [], "fullName": "Customer GraphQL API Integration Tests Customer Input Types should have AddressInput type in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have AddressInput type in schema"}, {"ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Customer GraphQL API Integration Tests Customer Operations should validate customer-related mutations exist", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should validate customer-related mutations exist"}, {"ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Customer GraphQL API Integration Tests Customer Operations should handle GraphQL validation errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle GraphQL validation errors"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504131655, "runtime": 890, "slow": false, "start": 1750504130765}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js", "testResults": [{"ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"], "duration": 57, "failureDetails": [], "failureMessages": [], "fullName": "Order Management Integration Tests Order GraphQL Schema Tests should have order query in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have order query in schema"}, {"ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "Order Management Integration Tests Order GraphQL Schema Tests should have orders query in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have orders query in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 3, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504132477, "runtime": 818, "slow": false, "start": 1750504131659}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js", "testResults": [{"ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "duration": 59, "failureDetails": [], "failureMessages": [], "fullName": "Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have updateOrderStatus mutation in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have updateOrderStatus mutation in schema"}, {"ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "duration": 24, "failureDetails": [], "failureMessages": [], "fullName": "Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have Order type with orderStatus field in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have Order type with orderStatus field in schema"}, {"ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "duration": 25, "failureDetails": [], "failureMessages": [], "fullName": "Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have order status enum values in schema", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should have order status enum values in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 4, "numPendingTests": 5, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504133295, "runtime": 815, "slow": false, "start": 1750504132480}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js", "testResults": [{"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests GraphQL Endpoint should respond to GraphQL endpoint", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should respond to GraphQL endpoint"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests GraphQL Endpoint should handle invalid GraphQL queries", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle invalid GraphQL queries"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Restaurant Schema"], "duration": 56, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Restaurant Schema should have Restaurant type in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have Restaurant type in schema"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle simple queries", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle simple queries"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Basic GraphQL Operations should validate GraphQL syntax", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should validate GraphQL syntax"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle empty queries", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty queries"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "duration": 25, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Schema Introspection should support schema introspection", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should support schema introspection"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "duration": 24, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Schema Introspection should list available queries", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should list available queries"}, {"ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "Restaurant GraphQL API Integration Tests Schema Introspection should list available mutations", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should list available mutations"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504134173, "runtime": 875, "slow": false, "start": 1750504133298}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js", "testResults": [{"ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"], "duration": 55, "failureDetails": [], "failureMessages": [], "fullName": "Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have order subscription in schema", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should have order subscription in schema"}, {"ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"], "duration": 25, "failureDetails": [], "failureMessages": [], "fullName": "Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have mutation types in schema", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should have mutation types in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 1, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1750504134918, "runtime": 742, "slow": false, "start": 1750504134176}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js", "testResults": [{"ancestorTitles": ["Payment System Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "duration": 60, "failureDetails": [], "failureMessages": [], "fullName": "Payment System Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have payment related types in schema"}], "displayName": {"name": "INTEGRATION", "color": "green"}, "failureMessage": null}], "wasInterrupted": false, "processedAt": "2025-06-21T11:08:55.322Z", "environment": {"nodeVersion": "v18.20.5", "platform": "linux", "arch": "x64", "env": "test"}, "summary": {"total": 52, "passed": 24, "failed": 0, "skipped": 28, "duration": 14520, "successRate": "46.15", "byType": {"INTEGRATION": {"total": 52, "passed": 24, "failed": 0, "skipped": 28, "duration": 1263}}}, "recommendations": [{"type": "quality", "priority": "high", "message": "测试成功率 46.15% 低于推荐的90%，需要修复失败的测试"}]}