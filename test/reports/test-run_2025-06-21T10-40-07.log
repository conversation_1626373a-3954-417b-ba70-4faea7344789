[2025-06-21T10:40:07.239Z] ================================================================================
[2025-06-21T10:40:07.239Z] Test Run Started
[2025-06-21T10:40:07.239Z] Timestamp: 2025-06-21T10:40:07.239Z
[2025-06-21T10:40:07.239Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:40:07.239Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:40:07.239Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:40:07.239Z] ================================================================================
[2025-06-21T10:40:07.244Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:40:07.244Z] Total Test Suites: 12
[2025-06-21T10:40:07.244Z] Test Environment: test
[2025-06-21T10:40:07.308Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:40:07.308Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.308Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:40:07.308Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.308Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:40:07.308Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:07.309Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:40:07.309Z] Suite Display Name: UNIT
[2025-06-21T10:40:08.575Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:40:08.575Z] Suite Duration: 1123ms
[2025-06-21T10:40:08.575Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T10:40:08.580Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:40:08.580Z] Suite Duration: 1112ms
[2025-06-21T10:40:08.580Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:40:08.581Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:40:08.581Z] Suite Duration: 1114ms
[2025-06-21T10:40:08.581Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:40:08.583Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:40:08.583Z] Suite Duration: 1138ms
[2025-06-21T10:40:08.583Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T10:40:08.623Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T10:40:08.623Z]   Module: whatsapp
[2025-06-21T10:40:08.623Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T10:40:08.656Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:40:08.656Z] Suite Duration: 1206ms
[2025-06-21T10:40:08.656Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T10:40:08.801Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:40:08.801Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T10:40:08.801Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:40:08.801Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:40:08.801Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:40:08.801Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:40:08.801Z]   [CASE END] - Duration: 182ms
[2025-06-21T10:40:08.809Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:40:08.810Z] Suite Duration: 1329ms
[2025-06-21T10:40:08.810Z] Suite Results: 1 passed, 0 failed, 7 skipped
[2025-06-21T10:40:09.079Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:40:09.079Z] Suite Duration: 1631ms
[2025-06-21T10:40:09.079Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:40:09.093Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:40:09.094Z] Suite Duration: 1645ms
[2025-06-21T10:40:09.094Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:40:09.101Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:40:09.101Z] Suite Duration: 1643ms
[2025-06-21T10:40:09.101Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:40:09.165Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:40:09.165Z] Suite Duration: 1705ms
[2025-06-21T10:40:09.165Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:40:09.168Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:40:09.168Z] Suite Duration: 1726ms
[2025-06-21T10:40:09.168Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:40:09.495Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:40:09.495Z] Suite Duration: 2039ms
[2025-06-21T10:40:09.495Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:40:09.555Z] 
================================================================================
[2025-06-21T10:40:09.555Z] Test Run Finished
[2025-06-21T10:40:09.555Z] End Timestamp: 2025-06-21T10:40:09.555Z
[2025-06-21T10:40:09.555Z] Total Duration: 2316ms (2.32s)
[2025-06-21T10:40:09.555Z] 
[STATISTICS]
[2025-06-21T10:40:09.555Z] Total Test Suites: 12
[2025-06-21T10:40:09.555Z] Passed Test Suites: 1
[2025-06-21T10:40:09.555Z] Failed Test Suites: 0
[2025-06-21T10:40:09.555Z] Total Tests: 94
[2025-06-21T10:40:09.555Z] Passed Tests: 1
[2025-06-21T10:40:09.555Z] Failed Tests: 0
[2025-06-21T10:40:09.555Z] Skipped Tests: 93
[2025-06-21T10:40:09.555Z] Success Rate: 1.06%
[2025-06-21T10:40:09.555Z] Overall Result: FAILURE
[2025-06-21T10:40:09.555Z] ================================================================================
