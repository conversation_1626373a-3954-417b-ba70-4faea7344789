[2025-06-21T10:53:50.807Z] ================================================================================
[2025-06-21T10:53:50.807Z] Test Run Started
[2025-06-21T10:53:50.807Z] Timestamp: 2025-06-21T10:53:50.807Z
[2025-06-21T10:53:50.807Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:53:50.807Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:53:50.807Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:53:50.807Z] ================================================================================
[2025-06-21T10:53:50.811Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:53:50.811Z] Total Test Suites: 14
[2025-06-21T10:53:50.811Z] Test Environment: test
[2025-06-21T10:53:50.818Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:53:50.818Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:53.191Z] 
  [CASE START] - TC-674469B3: should process incoming message and create session
[2025-06-21T10:53:53.192Z]   Module: whatsapp
[2025-06-21T10:53:53.192Z]   Full Path: WhatsApp Webhook Integration › should process incoming message and create session
[2025-06-21T10:53:53.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:53.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:53.192Z] 
  [CASE START] - TC-432D970F: should reject webhook with invalid signature
[2025-06-21T10:53:53.192Z]   Module: whatsapp
[2025-06-21T10:53:53.192Z]   Full Path: WhatsApp Webhook Integration › should reject webhook with invalid signature
[2025-06-21T10:53:53.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:53.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:53.192Z] 
  [CASE START] - TC-3376EAD8: should handle malformed webhook payload
[2025-06-21T10:53:53.192Z]   Module: whatsapp
[2025-06-21T10:53:53.192Z]   Full Path: WhatsApp Webhook Integration › should handle malformed webhook payload
[2025-06-21T10:53:53.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:53.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:53.192Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:53:53.192Z] Suite Duration: 2355ms
[2025-06-21T10:53:53.192Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:53:53.193Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:53:53.193Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:53.339Z] 
  [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-21T10:53:53.339Z]   Module: refund
[2025-06-21T10:53:53.339Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-21T10:53:53.339Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:53.339Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:53.339Z] 
  [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-21T10:53:53.339Z]   Module: refund
[2025-06-21T10:53:53.339Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-21T10:53:53.339Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:53.339Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:53.339Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:53:53.339Z] Suite Duration: 141ms
[2025-06-21T10:53:53.339Z] Suite Results: 0 passed, 0 failed, 2 skipped
[2025-06-21T10:53:53.340Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:53:53.340Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:54.376Z] 
  [CASE START] - TC-2E236918: should create real payment intent with Stripe
[2025-06-21T10:53:54.376Z]   Module: real
[2025-06-21T10:53:54.376Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should create real payment intent with Stripe
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-18AE2E8F: should retrieve real payment intent status
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should retrieve real payment intent status
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-6AADE1B7: should handle payment with test card
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle payment with test card
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-54B75689: should handle declined card
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle declined card
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-332A1C6D: should create real Stripe customer
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should create real Stripe customer
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-164683AD: should retrieve real Stripe customer
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should retrieve real Stripe customer
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-5FD44F7E: should process real webhook signature validation
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Real Webhook Processing › should process real webhook signature validation
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-74FBC87B: should handle invalid amount
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid amount
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] 
  [CASE START] - TC-56B8BFFE: should handle invalid currency
[2025-06-21T10:53:54.377Z]   Module: real
[2025-06-21T10:53:54.377Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid currency
[2025-06-21T10:53:54.377Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:54.377Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:54.377Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:53:54.377Z] Suite Duration: 1033ms
[2025-06-21T10:53:54.377Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:53:54.378Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:53:54.378Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:55.174Z] 
  [CASE START] - TC-3CAE5E97: should create a new order
[2025-06-21T10:53:55.175Z]   Module: graphql
[2025-06-21T10:53:55.175Z]   Full Path: Order GraphQL Mutations › should create a new order
[2025-06-21T10:53:55.175Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:55.175Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:55.175Z] 
  [CASE START] - TC-6385F57E: should fail to create order with invalid restaurant ID
[2025-06-21T10:53:55.175Z]   Module: graphql
[2025-06-21T10:53:55.175Z]   Full Path: Order GraphQL Mutations › should fail to create order with invalid restaurant ID
[2025-06-21T10:53:55.175Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:55.175Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:55.175Z] 
  [CASE START] - TC-7D51EFC9: should fail to create order without authentication
[2025-06-21T10:53:55.175Z]   Module: graphql
[2025-06-21T10:53:55.175Z]   Full Path: Order GraphQL Mutations › should fail to create order without authentication
[2025-06-21T10:53:55.175Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:55.175Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:55.175Z] 
  [CASE START] - TC-7912DF71: should update order status
[2025-06-21T10:53:55.175Z]   Module: graphql
[2025-06-21T10:53:55.175Z]   Full Path: Order GraphQL Mutations › should update order status
[2025-06-21T10:53:55.175Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:55.175Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:55.175Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:53:55.175Z] Suite Duration: 794ms
[2025-06-21T10:53:55.175Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:53:55.175Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:53:55.175Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:56.555Z] 
  [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-21T10:53:56.555Z]   Module: graphql
[2025-06-21T10:53:56.555Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-21T10:53:56.849Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type in schema"
[2025-06-21T10:53:56.849Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:56.849Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:56.849Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:56.849Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-21T10:53:56.850Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T10:53:56.850Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:56.850Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:56.850Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:56.850Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:56.850Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:56.850Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:56.850Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:56.850Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:56.850Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:56.850Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:56.850Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:56.850Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:56.850Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:56.850Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:56.850Z]   [CASE END] - Duration: 295ms
[2025-06-21T10:53:56.850Z] 
  [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-21T10:53:56.850Z]   Module: graphql
[2025-06-21T10:53:56.850Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-21T10:53:56.882Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderStatus enum in schema"
[2025-06-21T10:53:56.882Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:56.882Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:56.882Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:56.882Z]   [Act] - Step: Executing test logic for "should have OrderStatus enum in schema"
[2025-06-21T10:53:56.882Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T10:53:56.882Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:56.882Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:56.882Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:56.882Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:56.882Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:56.882Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:56.882Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:56.882Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:56.882Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:56.882Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:56.882Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:56.882Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:56.882Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:56.882Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:56.882Z]   [CASE END] - Duration: 32ms
[2025-06-21T10:53:56.882Z] 
  [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-21T10:53:56.882Z]   Module: graphql
[2025-06-21T10:53:56.882Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-21T10:53:56.914Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderInput type in schema"
[2025-06-21T10:53:56.914Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:56.914Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:56.914Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:56.914Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-21T10:53:56.914Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T10:53:56.914Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:56.914Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:56.914Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:56.914Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:56.914Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:56.914Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:56.914Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:56.914Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:56.914Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:56.914Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:56.914Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:56.914Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:56.914Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:56.914Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:56.914Z]   [CASE END] - Duration: 31ms
[2025-06-21T10:53:56.916Z] 
  [CASE START] - TC-1EE21EDD: should validate GraphQL order operations
[2025-06-21T10:53:56.916Z]   Module: graphql
[2025-06-21T10:53:56.916Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should validate GraphQL order operations
[2025-06-21T10:53:56.916Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:56.916Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:56.916Z] 
  [CASE START] - TC-8227B16: should handle malformed order queries
[2025-06-21T10:53:56.916Z]   Module: graphql
[2025-06-21T10:53:56.916Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should handle malformed order queries
[2025-06-21T10:53:56.916Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:56.916Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:56.916Z] 
  [CASE START] - TC-C2E23B4: should validate required arguments
[2025-06-21T10:53:56.916Z]   Module: graphql
[2025-06-21T10:53:56.916Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should validate required arguments
[2025-06-21T10:53:56.916Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:53:56.916Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:53:56.916Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:53:56.916Z] Suite Duration: 1736ms
[2025-06-21T10:53:56.916Z] Suite Results: 3 passed, 0 failed, 3 skipped
[2025-06-21T10:53:56.917Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:53:56.917Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:58.207Z] 
  [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-21T10:53:58.207Z]   Module: graphql
[2025-06-21T10:53:58.207Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-21T10:53:58.264Z]   [Arrange] - Precondition: Test environment prepared for "should respond to GraphQL introspection query"
[2025-06-21T10:53:58.264Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:58.264Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:58.264Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:58.264Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-21T10:53:58.264Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-21T10:53:58.264Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:58.264Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:58.264Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:58.264Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:58.264Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:58.264Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:58.264Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:58.264Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:58.264Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T10:53:58.264Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T10:53:58.264Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:58.264Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:58.264Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:58.264Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:58.264Z]   [CASE END] - Duration: 57ms
[2025-06-21T10:53:58.264Z] 
  [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-21T10:53:58.264Z]   Module: graphql
[2025-06-21T10:53:58.264Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-21T10:53:58.288Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T10:53:58.288Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:58.288Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:58.288Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:58.288Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:53:58.288Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-21T10:53:58.288Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:58.288Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:58.288Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:58.288Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:58.288Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:58.288Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:58.288Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:58.288Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:58.288Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:58.288Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:58.288Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:58.288Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:58.288Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:58.288Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:58.288Z]   [CASE END] - Duration: 23ms
[2025-06-21T10:53:58.289Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:53:58.289Z] Suite Duration: 1369ms
[2025-06-21T10:53:58.289Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:53:58.289Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:53:58.289Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:58.993Z] 
  [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-21T10:53:58.993Z]   Module: graphql
[2025-06-21T10:53:58.993Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:53:59.051Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T10:53:59.051Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:59.051Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:59.051Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:59.051Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:53:59.051Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-21T10:53:59.051Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:59.051Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:59.051Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:59.051Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:59.051Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:59.051Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:59.051Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:59.051Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:59.051Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:59.051Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:59.051Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:59.051Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:59.051Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:59.051Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:59.051Z]   [CASE END] - Duration: 58ms
[2025-06-21T10:53:59.051Z] 
  [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-21T10:53:59.051Z]   Module: graphql
[2025-06-21T10:53:59.051Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-21T10:53:59.078Z]   [Arrange] - Precondition: Test environment prepared for "should have query types in schema"
[2025-06-21T10:53:59.078Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:59.078Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:59.078Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:59.078Z]   [Act] - Step: Executing test logic for "should have query types in schema"
[2025-06-21T10:53:59.078Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-21T10:53:59.078Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:59.078Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:59.078Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:59.078Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:59.078Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:59.078Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:59.078Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:59.078Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:59.078Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:59.078Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:59.078Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:59.078Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:59.078Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:59.078Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:59.078Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:53:59.079Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:53:59.079Z] Suite Duration: 786ms
[2025-06-21T10:53:59.079Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:53:59.079Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:53:59.079Z] Suite Display Name: INTEGRATION
[2025-06-21T10:53:59.797Z] 
  [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-21T10:53:59.797Z]   Module: graphql
[2025-06-21T10:53:59.797Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:53:59.865Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T10:53:59.866Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:59.866Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:59.866Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:59.866Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:53:59.866Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-21T10:53:59.866Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:59.866Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:59.866Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:59.866Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:59.866Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:59.866Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:59.866Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:59.866Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:59.866Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:59.866Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:59.866Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:59.866Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:59.866Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:59.866Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:59.866Z]   [CASE END] - Duration: 69ms
[2025-06-21T10:53:59.866Z] 
  [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-21T10:53:59.866Z]   Module: graphql
[2025-06-21T10:53:59.866Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-21T10:53:59.895Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T10:53:59.895Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:53:59.895Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:53:59.895Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:53:59.895Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:53:59.895Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-21T10:53:59.895Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:53:59.895Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:53:59.895Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:53:59.895Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:53:59.895Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:53:59.895Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:53:59.895Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:53:59.895Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:53:59.895Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:53:59.895Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:53:59.895Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:53:59.895Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:53:59.895Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:53:59.895Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:53:59.895Z]   [CASE END] - Duration: 28ms
[2025-06-21T10:53:59.896Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:53:59.896Z] Suite Duration: 815ms
[2025-06-21T10:53:59.896Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:53:59.896Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:53:59.896Z] Suite Display Name: INTEGRATION
[2025-06-21T10:54:00.584Z] 
  [CASE START] - TC-50629841: should have updateOrderStatus mutation in schema
[2025-06-21T10:54:00.584Z]   Module: graphql
[2025-06-21T10:54:00.584Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-21T10:54:00.642Z]   [Arrange] - Precondition: Test environment prepared for "should have updateOrderStatus mutation in schema"
[2025-06-21T10:54:00.642Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:00.642Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:00.642Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:00.642Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-21T10:54:00.642Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T10:54:00.642Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:00.642Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:00.642Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:00.642Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:00.642Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:00.642Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:00.642Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:00.642Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:00.642Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:00.642Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:00.642Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:00.642Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:00.642Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:00.642Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:00.642Z]   [CASE END] - Duration: 58ms
[2025-06-21T10:54:00.642Z] 
  [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-21T10:54:00.642Z]   Module: graphql
[2025-06-21T10:54:00.642Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-21T10:54:00.668Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type with orderStatus field in schema"
[2025-06-21T10:54:00.668Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:00.668Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:00.668Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:00.668Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-21T10:54:00.668Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T10:54:00.668Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:00.668Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:00.668Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:00.668Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:00.668Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:00.668Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:00.668Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:00.668Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:00.668Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:00.668Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:00.668Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:00.668Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:00.668Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:00.668Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:00.668Z]   [CASE END] - Duration: 25ms
[2025-06-21T10:54:00.668Z] 
  [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-21T10:54:00.668Z]   Module: graphql
[2025-06-21T10:54:00.668Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-21T10:54:00.692Z]   [Arrange] - Precondition: Test environment prepared for "should have order status enum values in schema"
[2025-06-21T10:54:00.692Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:00.692Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:00.692Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:00.692Z]   [Act] - Step: Executing test logic for "should have order status enum values in schema"
[2025-06-21T10:54:00.692Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T10:54:00.692Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:00.692Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:00.692Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:00.692Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:00.692Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:00.692Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:00.692Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:00.692Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:00.692Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:00.692Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:00.692Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:00.692Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:00.692Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:00.692Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:00.692Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:54:00.693Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:54:00.693Z] Suite Duration: 794ms
[2025-06-21T10:54:00.693Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-21T10:54:00.694Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:54:00.694Z] Suite Display Name: INTEGRATION
[2025-06-21T10:54:01.498Z] 
  [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-21T10:54:01.498Z]   Module: graphql
[2025-06-21T10:54:01.498Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-21T10:54:01.565Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T10:54:01.565Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:01.565Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:01.565Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:01.565Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:54:01.565Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T10:54:01.565Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:01.565Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:01.565Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:01.565Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:01.565Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:01.565Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:01.565Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:01.565Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:01.565Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:01.565Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:01.565Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:01.565Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:01.565Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:01.565Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:01.565Z]   [CASE END] - Duration: 66ms
[2025-06-21T10:54:01.565Z] 
  [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-21T10:54:01.565Z]   Module: graphql
[2025-06-21T10:54:01.565Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-21T10:54:01.589Z]   [Arrange] - Precondition: Test environment prepared for "should support schema introspection"
[2025-06-21T10:54:01.589Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:01.589Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:01.589Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:01.589Z]   [Act] - Step: Executing test logic for "should support schema introspection"
[2025-06-21T10:54:01.589Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T10:54:01.589Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:01.589Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:01.589Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:01.589Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:01.589Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:01.589Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:01.589Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:01.589Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:01.589Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T10:54:01.589Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T10:54:01.589Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:01.589Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:01.589Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:01.590Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:01.590Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:54:01.590Z] 
  [CASE START] - TC-6C7A3797: should list available queries
[2025-06-21T10:54:01.590Z]   Module: graphql
[2025-06-21T10:54:01.590Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-21T10:54:01.613Z]   [Arrange] - Precondition: Test environment prepared for "should list available queries"
[2025-06-21T10:54:01.613Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:01.613Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:01.613Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:01.613Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-21T10:54:01.613Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T10:54:01.613Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:01.613Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:01.613Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:01.613Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:01.613Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:01.613Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:01.613Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:01.613Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:01.613Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T10:54:01.613Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T10:54:01.613Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:01.613Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:01.613Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:01.613Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:01.613Z]   [CASE END] - Duration: 23ms
[2025-06-21T10:54:01.614Z] 
  [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-21T10:54:01.614Z]   Module: graphql
[2025-06-21T10:54:01.614Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-21T10:54:01.639Z]   [Arrange] - Precondition: Test environment prepared for "should list available mutations"
[2025-06-21T10:54:01.639Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:01.639Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:01.639Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:01.639Z]   [Act] - Step: Executing test logic for "should list available mutations"
[2025-06-21T10:54:01.640Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T10:54:01.640Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:01.640Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:01.640Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:01.640Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:01.640Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:01.640Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:01.640Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:01.640Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:01.640Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T10:54:01.640Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T10:54:01.640Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:01.640Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:01.640Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:01.640Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:01.640Z]   [CASE END] - Duration: 25ms
[2025-06-21T10:54:01.641Z] 
  [CASE START] - TC-387C915A: should respond to GraphQL endpoint
[2025-06-21T10:54:01.641Z]   Module: graphql
[2025-06-21T10:54:01.641Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should respond to GraphQL endpoint
[2025-06-21T10:54:01.641Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:01.641Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:01.641Z] 
  [CASE START] - TC-21545EE6: should handle invalid GraphQL queries
[2025-06-21T10:54:01.641Z]   Module: graphql
[2025-06-21T10:54:01.641Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should handle invalid GraphQL queries
[2025-06-21T10:54:01.641Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:01.641Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:01.641Z] 
  [CASE START] - TC-18BF1AAD: should handle simple queries
[2025-06-21T10:54:01.641Z]   Module: graphql
[2025-06-21T10:54:01.641Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle simple queries
[2025-06-21T10:54:01.641Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:01.641Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:01.641Z] 
  [CASE START] - TC-14898FE1: should validate GraphQL syntax
[2025-06-21T10:54:01.641Z]   Module: graphql
[2025-06-21T10:54:01.641Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should validate GraphQL syntax
[2025-06-21T10:54:01.641Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:01.641Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:01.641Z] 
  [CASE START] - TC-63BF36C2: should handle empty queries
[2025-06-21T10:54:01.641Z]   Module: graphql
[2025-06-21T10:54:01.641Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle empty queries
[2025-06-21T10:54:01.641Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:01.641Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:01.641Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:54:01.641Z] Suite Duration: 944ms
[2025-06-21T10:54:01.641Z] Suite Results: 4 passed, 0 failed, 5 skipped
[2025-06-21T10:54:01.641Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:54:01.641Z] Suite Display Name: INTEGRATION
[2025-06-21T10:54:02.336Z] 
  [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-21T10:54:02.336Z]   Module: order
[2025-06-21T10:54:02.336Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-21T10:54:02.396Z]   [Arrange] - Precondition: Test environment prepared for "should have order query in schema"
[2025-06-21T10:54:02.396Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:02.396Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:02.396Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:02.396Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-21T10:54:02.396Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-21T10:54:02.396Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:02.396Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:02.396Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:02.396Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:02.396Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:02.396Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:02.396Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:02.396Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:02.396Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:02.396Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:02.396Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:02.396Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:02.396Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:02.396Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:02.396Z]   [CASE END] - Duration: 60ms
[2025-06-21T10:54:02.396Z] 
  [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-21T10:54:02.396Z]   Module: order
[2025-06-21T10:54:02.396Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-21T10:54:02.423Z]   [Arrange] - Precondition: Test environment prepared for "should have orders query in schema"
[2025-06-21T10:54:02.423Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:02.423Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:02.423Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:02.423Z]   [Act] - Step: Executing test logic for "should have orders query in schema"
[2025-06-21T10:54:02.423Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-21T10:54:02.423Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:02.423Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:02.423Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:02.423Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:02.423Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:02.423Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:02.423Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:02.423Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:02.423Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:02.423Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:02.424Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:02.424Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:02.424Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:02.424Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:02.424Z]   [CASE END] - Duration: 27ms
[2025-06-21T10:54:02.425Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:54:02.425Z] Suite Duration: 780ms
[2025-06-21T10:54:02.425Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:54:02.425Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:54:02.425Z] Suite Display Name: INTEGRATION
[2025-06-21T10:54:03.201Z] 
  [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-21T10:54:03.201Z]   Module: graphql
[2025-06-21T10:54:03.201Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-21T10:54:03.260Z]   [Arrange] - Precondition: Test environment prepared for "should have Customer type in schema"
[2025-06-21T10:54:03.260Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:03.260Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:03.260Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:03.260Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-21T10:54:03.260Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T10:54:03.260Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:03.260Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:03.260Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:03.260Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:03.260Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:03.260Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:03.260Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:03.260Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:03.260Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:03.260Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:03.260Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:03.260Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:03.260Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:03.260Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:03.260Z]   [CASE END] - Duration: 58ms
[2025-06-21T10:54:03.260Z] 
  [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-21T10:54:03.260Z]   Module: graphql
[2025-06-21T10:54:03.260Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-21T10:54:03.287Z]   [Arrange] - Precondition: Test environment prepared for "should have Address type in schema"
[2025-06-21T10:54:03.287Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:03.287Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:03.287Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:03.287Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-21T10:54:03.287Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T10:54:03.287Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:03.287Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:03.287Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:03.287Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:03.287Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:03.287Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:03.287Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:03.287Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:03.288Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:03.288Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:03.288Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:03.288Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:03.288Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:03.288Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:03.288Z]   [CASE END] - Duration: 27ms
[2025-06-21T10:54:03.288Z] 
  [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-21T10:54:03.288Z]   Module: graphql
[2025-06-21T10:54:03.288Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-21T10:54:03.315Z]   [Arrange] - Precondition: Test environment prepared for "should have AddressInput type in schema"
[2025-06-21T10:54:03.315Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:03.315Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:03.315Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:03.315Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-21T10:54:03.315Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T10:54:03.315Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:03.315Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:03.315Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:03.315Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:03.315Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:03.315Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:03.315Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:03.315Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:03.315Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:03.315Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:03.315Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:03.315Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:03.315Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:03.315Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:03.315Z]   [CASE END] - Duration: 27ms
[2025-06-21T10:54:03.316Z] 
  [CASE START] - TC-2C17DA72: should validate customer-related mutations exist
[2025-06-21T10:54:03.316Z]   Module: graphql
[2025-06-21T10:54:03.316Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should validate customer-related mutations exist
[2025-06-21T10:54:03.316Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:03.316Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:03.316Z] 
  [CASE START] - TC-D206B8: should handle GraphQL validation errors
[2025-06-21T10:54:03.316Z]   Module: graphql
[2025-06-21T10:54:03.316Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should handle GraphQL validation errors
[2025-06-21T10:54:03.316Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:54:03.316Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:54:03.316Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:54:03.316Z] Suite Duration: 889ms
[2025-06-21T10:54:03.317Z] Suite Results: 3 passed, 0 failed, 2 skipped
[2025-06-21T10:54:03.317Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:54:03.317Z] Suite Display Name: INTEGRATION
[2025-06-21T10:54:04.099Z] 
  [CASE START] - TC-7DB19ADA: should have order subscription in schema
[2025-06-21T10:54:04.100Z]   Module: graphql
[2025-06-21T10:54:04.100Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have order subscription in schema
[2025-06-21T10:54:04.153Z]   [Arrange] - Precondition: Test environment prepared for "should have order subscription in schema"
[2025-06-21T10:54:04.153Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:04.153Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:04.153Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:04.153Z]   [Act] - Step: Executing test logic for "should have order subscription in schema"
[2025-06-21T10:54:04.153Z]   [Act Log] - Loading target module: /test/integration/order/orderNotifications.js
[2025-06-21T10:54:04.153Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:04.153Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:04.153Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:04.153Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:04.153Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:04.153Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:04.153Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:04.153Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:04.153Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:04.153Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:04.153Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:04.153Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:04.153Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:04.153Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:04.153Z]   [CASE END] - Duration: 54ms
[2025-06-21T10:54:04.154Z] 
  [CASE START] - TC-7B992587: should have mutation types in schema
[2025-06-21T10:54:04.154Z]   Module: graphql
[2025-06-21T10:54:04.154Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have mutation types in schema
[2025-06-21T10:54:04.178Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T10:54:04.178Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:04.178Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:04.178Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:04.178Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:54:04.178Z]   [Act Log] - Loading target module: /test/integration/order/orderNotifications.js
[2025-06-21T10:54:04.178Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:04.178Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:04.178Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:04.178Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:04.178Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:04.178Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:04.178Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:04.178Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:04.178Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:04.178Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:04.178Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:04.178Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:04.178Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:04.178Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:04.178Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:54:04.179Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:54:04.179Z] Suite Duration: 860ms
[2025-06-21T10:54:04.179Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:54:04.179Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:54:04.179Z] Suite Display Name: INTEGRATION
[2025-06-21T10:54:04.855Z] 
  [CASE START] - TC-528B5677: should have payment related types in schema
[2025-06-21T10:54:04.855Z]   Module: graphql
[2025-06-21T10:54:04.855Z]   Full Path: Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:54:04.909Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T10:54:04.909Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T10:54:04.909Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T10:54:04.909Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T10:54:04.909Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:54:04.909Z]   [Act Log] - Loading target module: /test/integration/payment/paymentSystem.js
[2025-06-21T10:54:04.909Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T10:54:04.909Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T10:54:04.909Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:54:04.909Z]   [Act Log] - Method execution completed successfully
[2025-06-21T10:54:04.909Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T10:54:04.909Z]   [Act Log] - All function calls returned expected types
[2025-06-21T10:54:04.909Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T10:54:04.909Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:54:04.909Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T10:54:04.909Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T10:54:04.909Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T10:54:04.909Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T10:54:04.909Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T10:54:04.909Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:54:04.910Z]   [CASE END] - Duration: 54ms
[2025-06-21T10:54:04.910Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:54:04.910Z] Suite Duration: 728ms
[2025-06-21T10:54:04.910Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T10:54:04.919Z] 
================================================================================
[2025-06-21T10:54:04.919Z] Test Run Finished
[2025-06-21T10:54:04.919Z] End Timestamp: 2025-06-21T10:54:04.919Z
[2025-06-21T10:54:04.919Z] Total Duration: 14112ms (14.11s)
[2025-06-21T10:54:04.919Z] 
[STATISTICS]
[2025-06-21T10:54:04.919Z] Total Test Suites: 14
[2025-06-21T10:54:04.919Z] Passed Test Suites: 10
[2025-06-21T10:54:04.919Z] Failed Test Suites: 0
[2025-06-21T10:54:04.919Z] Total Tests: 52
[2025-06-21T10:54:04.919Z] Passed Tests: 24
[2025-06-21T10:54:04.919Z] Failed Tests: 0
[2025-06-21T10:54:04.919Z] Skipped Tests: 28
[2025-06-21T10:54:04.919Z] Success Rate: 46.15%
[2025-06-21T10:54:04.919Z] Overall Result: FAILURE
[2025-06-21T10:54:04.919Z] ================================================================================
