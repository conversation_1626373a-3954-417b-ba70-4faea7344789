[2025-06-21T10:41:40.783Z] ================================================================================
[2025-06-21T10:41:40.783Z] Test Run Started
[2025-06-21T10:41:40.783Z] Timestamp: 2025-06-21T10:41:40.783Z
[2025-06-21T10:41:40.783Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:41:40.783Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:41:40.783Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:41:40.783Z] ================================================================================
[2025-06-21T10:41:40.787Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:41:40.787Z] Total Test Suites: 12
[2025-06-21T10:41:40.787Z] Test Environment: test
[2025-06-21T10:41:40.851Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:41:40.852Z] Suite Display Name: UNIT
[2025-06-21T10:41:42.055Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:41:42.055Z] Suite Duration: 1057ms
[2025-06-21T10:41:42.055Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:41:42.070Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:41:42.070Z] Suite Duration: 1073ms
[2025-06-21T10:41:42.070Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T10:41:42.079Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:41:42.079Z] Suite Duration: 1049ms
[2025-06-21T10:41:42.079Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:41:42.090Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T10:41:42.090Z]   Module: whatsapp
[2025-06-21T10:41:42.091Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T10:41:42.143Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:41:42.143Z] Suite Duration: 1138ms
[2025-06-21T10:41:42.143Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T10:41:42.203Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:41:42.203Z] Suite Duration: 1201ms
[2025-06-21T10:41:42.204Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T10:41:42.271Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:42.272Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T10:41:42.272Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:42.272Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:42.272Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:42.272Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:42.272Z]   [CASE END] - Duration: 186ms
[2025-06-21T10:41:42.272Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T10:41:42.272Z]   Module: whatsapp
[2025-06-21T10:41:42.272Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T10:41:42.327Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:42.327Z]   [Act] - Step: Executing test logic for "should have required methods"
[2025-06-21T10:41:42.327Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:42.327Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:42.327Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:42.327Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:42.327Z]   [CASE END] - Duration: 55ms
[2025-06-21T10:41:42.327Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T10:41:42.327Z]   Module: whatsapp
[2025-06-21T10:41:42.327Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T10:41:42.382Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T10:41:42.382Z]   Module: whatsapp
[2025-06-21T10:41:42.382Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T10:41:42.382Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:42.382Z]   [Act] - Step: Executing test logic for "should have configuration properties or be configurable"
[2025-06-21T10:41:42.382Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:42.382Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:42.382Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:42.382Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:42.382Z]   [CASE END] - Duration: 54ms
[2025-06-21T10:41:42.441Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T10:41:42.441Z]   Module: whatsapp
[2025-06-21T10:41:42.441Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T10:41:42.491Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T10:41:42.491Z]   Module: whatsapp
[2025-06-21T10:41:42.491Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T10:41:42.492Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:42.492Z]   [Act] - Step: Executing test logic for "should have retry configuration or error handling"
[2025-06-21T10:41:42.492Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:42.492Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:42.492Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:42.492Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:42.492Z]   [CASE END] - Duration: 50ms
[2025-06-21T10:41:42.526Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:41:42.527Z] Suite Duration: 1526ms
[2025-06-21T10:41:42.527Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:41:42.562Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:41:42.562Z] Suite Duration: 1549ms
[2025-06-21T10:41:42.562Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:41:42.575Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:41:42.575Z] Suite Duration: 1566ms
[2025-06-21T10:41:42.575Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:41:42.639Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:41:42.639Z] Suite Duration: 1624ms
[2025-06-21T10:41:42.639Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:41:42.719Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:41:42.719Z] Suite Duration: 1724ms
[2025-06-21T10:41:42.719Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:41:42.843Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T10:41:42.843Z]   Module: whatsapp
[2025-06-21T10:41:42.843Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T10:41:42.877Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:42.877Z]   [Act] - Step: Executing test logic for "should be able to call sendBasicText method"
[2025-06-21T10:41:42.877Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:42.877Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:42.877Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:42.877Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:42.877Z]   [CASE END] - Duration: 34ms
[2025-06-21T10:41:42.877Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T10:41:42.877Z]   Module: whatsapp
[2025-06-21T10:41:42.877Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T10:41:42.903Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:42.903Z]   [Act] - Step: Executing test logic for "should be able to call sendQuickReply method"
[2025-06-21T10:41:42.903Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:42.903Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:42.903Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:42.903Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:42.903Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:41:42.906Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:41:42.906Z] Suite Duration: 1882ms
[2025-06-21T10:41:42.906Z] Suite Results: 8 passed, 0 failed, 0 skipped
[2025-06-21T10:41:42.985Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:41:42.985Z] Suite Duration: 1988ms
[2025-06-21T10:41:42.985Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:41:43.040Z] 
================================================================================
[2025-06-21T10:41:43.040Z] Test Run Finished
[2025-06-21T10:41:43.040Z] End Timestamp: 2025-06-21T10:41:43.040Z
[2025-06-21T10:41:43.040Z] Total Duration: 2257ms (2.26s)
[2025-06-21T10:41:43.040Z] 
[STATISTICS]
[2025-06-21T10:41:43.040Z] Total Test Suites: 12
[2025-06-21T10:41:43.040Z] Passed Test Suites: 1
[2025-06-21T10:41:43.040Z] Failed Test Suites: 0
[2025-06-21T10:41:43.040Z] Total Tests: 94
[2025-06-21T10:41:43.040Z] Passed Tests: 8
[2025-06-21T10:41:43.040Z] Failed Tests: 0
[2025-06-21T10:41:43.040Z] Skipped Tests: 86
[2025-06-21T10:41:43.040Z] Success Rate: 8.51%
[2025-06-21T10:41:43.040Z] Overall Result: FAILURE
[2025-06-21T10:41:43.040Z] ================================================================================
