[2025-06-21T10:46:14.300Z] ================================================================================
[2025-06-21T10:46:14.300Z] Test Run Started
[2025-06-21T10:46:14.300Z] Timestamp: 2025-06-21T10:46:14.299Z
[2025-06-21T10:46:14.300Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:46:14.300Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:46:14.300Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:46:14.300Z] ================================================================================
[2025-06-21T10:46:14.304Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:46:14.304Z] Total Test Suites: 12
[2025-06-21T10:46:14.304Z] Test Environment: test
[2025-06-21T10:46:14.388Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:46:14.388Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.388Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:46:14.388Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.388Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:46:14.388Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:14.389Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:46:14.389Z] Suite Display Name: UNIT
[2025-06-21T10:46:16.063Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T10:46:16.064Z]   Module: restaurantstore
[2025-06-21T10:46:16.064Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T10:46:16.064Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.065Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.065Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T10:46:16.065Z]   Module: restaurantstore
[2025-06-21T10:46:16.065Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T10:46:16.065Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.065Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.065Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T10:46:16.065Z]   Module: restaurantstore
[2025-06-21T10:46:16.065Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T10:46:16.065Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.065Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.065Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T10:46:16.065Z]   Module: restaurantstore
[2025-06-21T10:46:16.065Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T10:46:16.065Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.065Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.065Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T10:46:16.065Z]   Module: restaurantstore
[2025-06-21T10:46:16.065Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T10:46:16.066Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.066Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.066Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T10:46:16.066Z]   Module: restaurantstore
[2025-06-21T10:46:16.066Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T10:46:16.066Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.066Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.066Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T10:46:16.066Z]   Module: restaurantstore
[2025-06-21T10:46:16.066Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T10:46:16.066Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.066Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.066Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T10:46:16.066Z]   Module: restaurantstore
[2025-06-21T10:46:16.066Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T10:46:16.066Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.066Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.066Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T10:46:16.066Z]   Module: restaurantstore
[2025-06-21T10:46:16.066Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T10:46:16.066Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.066Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.067Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:46:16.067Z] Suite Duration: 1438ms
[2025-06-21T10:46:16.067Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:46:16.068Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T10:46:16.068Z]   Module: sessionservice
[2025-06-21T10:46:16.068Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T10:46:16.068Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.068Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.068Z] 
  [CASE START] - ***********: should have required methods
[2025-06-21T10:46:16.068Z]   Module: sessionservice
[2025-06-21T10:46:16.068Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T10:46:16.068Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.068Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.068Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T10:46:16.068Z]   Module: sessionservice
[2025-06-21T10:46:16.068Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T10:46:16.068Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.068Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.068Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T10:46:16.068Z]   Module: sessionservice
[2025-06-21T10:46:16.068Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T10:46:16.068Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.068Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.069Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T10:46:16.069Z]   Module: sessionservice
[2025-06-21T10:46:16.069Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T10:46:16.069Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.069Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.069Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T10:46:16.069Z]   Module: sessionservice
[2025-06-21T10:46:16.069Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T10:46:16.069Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.069Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.069Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T10:46:16.069Z]   Module: sessionservice
[2025-06-21T10:46:16.069Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T10:46:16.069Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.069Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.069Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T10:46:16.069Z]   Module: sessionservice
[2025-06-21T10:46:16.069Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T10:46:16.069Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.069Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.069Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T10:46:16.069Z]   Module: sessionservice
[2025-06-21T10:46:16.069Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T10:46:16.069Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.069Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.069Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T10:46:16.070Z]   Module: sessionservice
[2025-06-21T10:46:16.070Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T10:46:16.070Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.070Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.070Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T10:46:16.070Z]   Module: sessionservice
[2025-06-21T10:46:16.070Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T10:46:16.070Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.070Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.070Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:46:16.070Z] Suite Duration: 1450ms
[2025-06-21T10:46:16.070Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T10:46:16.185Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T10:46:16.185Z]   Module: whatsapp
[2025-06-21T10:46:16.185Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T10:46:16.186Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T10:46:16.186Z]   Module: linkgenerator
[2025-06-21T10:46:16.186Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T10:46:16.186Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.186Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.186Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T10:46:16.186Z]   Module: linkgenerator
[2025-06-21T10:46:16.186Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T10:46:16.186Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.186Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.186Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T10:46:16.186Z]   Module: linkgenerator
[2025-06-21T10:46:16.186Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T10:46:16.187Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.187Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.187Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T10:46:16.187Z]   Module: linkgenerator
[2025-06-21T10:46:16.187Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T10:46:16.187Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.187Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.187Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T10:46:16.187Z]   Module: linkgenerator
[2025-06-21T10:46:16.187Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T10:46:16.187Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.187Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.187Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T10:46:16.187Z]   Module: linkgenerator
[2025-06-21T10:46:16.187Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T10:46:16.187Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.187Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.188Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:46:16.188Z] Suite Duration: 1541ms
[2025-06-21T10:46:16.188Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:46:16.227Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T10:46:16.227Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.227Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T10:46:16.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.228Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T10:46:16.228Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T10:46:16.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.228Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T10:46:16.228Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T10:46:16.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.228Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.228Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T10:46:16.228Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.228Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T10:46:16.228Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.229Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T10:46:16.229Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.229Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T10:46:16.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.229Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T10:46:16.229Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.229Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T10:46:16.229Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.229Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.230Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T10:46:16.230Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.230Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T10:46:16.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.230Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T10:46:16.230Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.230Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T10:46:16.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.230Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T10:46:16.230Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.230Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T10:46:16.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.230Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T10:46:16.230Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.230Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T10:46:16.230Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.230Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.231Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T10:46:16.231Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.231Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T10:46:16.231Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.231Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.231Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T10:46:16.231Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.231Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T10:46:16.231Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.231Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.231Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T10:46:16.231Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.231Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T10:46:16.231Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.231Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.231Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T10:46:16.231Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.232Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T10:46:16.232Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.232Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.232Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T10:46:16.232Z]   Module: sessionidgenerator
[2025-06-21T10:46:16.232Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T10:46:16.232Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.232Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.232Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:46:16.232Z] Suite Duration: 1606ms
[2025-06-21T10:46:16.232Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T10:46:16.282Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T10:46:16.283Z]   Module: messagebuilders
[2025-06-21T10:46:16.284Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T10:46:16.284Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.284Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.284Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T10:46:16.284Z]   Module: messagebuilders
[2025-06-21T10:46:16.284Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T10:46:16.284Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.284Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.284Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T10:46:16.284Z]   Module: messagebuilders
[2025-06-21T10:46:16.284Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T10:46:16.284Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.284Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.284Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T10:46:16.284Z]   Module: messagebuilders
[2025-06-21T10:46:16.284Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T10:46:16.284Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.284Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.284Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T10:46:16.284Z]   Module: messagebuilders
[2025-06-21T10:46:16.284Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T10:46:16.284Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.284Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.284Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T10:46:16.285Z]   Module: messagebuilders
[2025-06-21T10:46:16.285Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T10:46:16.285Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.285Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.285Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T10:46:16.285Z]   Module: messagebuilders
[2025-06-21T10:46:16.285Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T10:46:16.285Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.285Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.285Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T10:46:16.285Z]   Module: messagebuilders
[2025-06-21T10:46:16.285Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T10:46:16.285Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.285Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.285Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T10:46:16.285Z]   Module: messagebuilders
[2025-06-21T10:46:16.285Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T10:46:16.285Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.285Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.285Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T10:46:16.285Z]   Module: messagebuilders
[2025-06-21T10:46:16.285Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T10:46:16.285Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.285Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.285Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T10:46:16.285Z]   Module: messagebuilders
[2025-06-21T10:46:16.285Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T10:46:16.286Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.286Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.286Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T10:46:16.286Z]   Module: messagebuilders
[2025-06-21T10:46:16.286Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T10:46:16.286Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.286Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.286Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T10:46:16.286Z]   Module: messagebuilders
[2025-06-21T10:46:16.286Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T10:46:16.286Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.286Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.286Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T10:46:16.286Z]   Module: messagebuilders
[2025-06-21T10:46:16.286Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T10:46:16.286Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.286Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.286Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T10:46:16.286Z]   Module: messagebuilders
[2025-06-21T10:46:16.286Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T10:46:16.286Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.286Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.286Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T10:46:16.286Z]   Module: messagebuilders
[2025-06-21T10:46:16.286Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T10:46:16.286Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.286Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.286Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:46:16.287Z] Suite Duration: 1642ms
[2025-06-21T10:46:16.287Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T10:46:16.470Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:16.471Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T10:46:16.471Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:16.471Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:16.471Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:16.471Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:16.471Z]   [CASE END] - Duration: 293ms
[2025-06-21T10:46:16.471Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T10:46:16.471Z]   Module: whatsapp
[2025-06-21T10:46:16.471Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T10:46:16.550Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:16.550Z]   [Act] - Step: Executing test logic for "should have required methods"
[2025-06-21T10:46:16.550Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:16.550Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:16.550Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:16.550Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:16.550Z]   [CASE END] - Duration: 77ms
[2025-06-21T10:46:16.551Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T10:46:16.551Z]   Module: whatsapp
[2025-06-21T10:46:16.551Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T10:46:16.644Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T10:46:16.644Z]   Module: whatsapp
[2025-06-21T10:46:16.644Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T10:46:16.644Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:16.644Z]   [Act] - Step: Executing test logic for "should have configuration properties or be configurable"
[2025-06-21T10:46:16.644Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:16.644Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:16.644Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:16.644Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:16.645Z]   [CASE END] - Duration: 90ms
[2025-06-21T10:46:16.729Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T10:46:16.729Z]   Module: whatsapp
[2025-06-21T10:46:16.729Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T10:46:16.770Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T10:46:16.771Z]   Module: order
[2025-06-21T10:46:16.771Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T10:46:16.771Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.771Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.771Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T10:46:16.771Z]   Module: order
[2025-06-21T10:46:16.771Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T10:46:16.771Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.771Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.771Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T10:46:16.771Z]   Module: order
[2025-06-21T10:46:16.771Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T10:46:16.771Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.771Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.771Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T10:46:16.771Z]   Module: order
[2025-06-21T10:46:16.771Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T10:46:16.771Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.771Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.771Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:46:16.772Z] Suite Duration: 2148ms
[2025-06-21T10:46:16.772Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:46:16.775Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T10:46:16.775Z]   Module: order
[2025-06-21T10:46:16.775Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T10:46:16.775Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.775Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.775Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T10:46:16.775Z]   Module: order
[2025-06-21T10:46:16.775Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T10:46:16.775Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.775Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.775Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T10:46:16.775Z]   Module: order
[2025-06-21T10:46:16.775Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T10:46:16.775Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.775Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.775Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T10:46:16.775Z]   Module: order
[2025-06-21T10:46:16.775Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T10:46:16.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.776Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:46:16.776Z]   Module: order
[2025-06-21T10:46:16.776Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:46:16.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.776Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:46:16.776Z] Suite Duration: 2170ms
[2025-06-21T10:46:16.776Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:46:16.821Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:46:16.821Z]   Module: order
[2025-06-21T10:46:16.822Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:46:16.822Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.822Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.822Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:46:16.822Z]   Module: order
[2025-06-21T10:46:16.822Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:46:16.822Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.822Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.822Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:46:16.822Z]   Module: order
[2025-06-21T10:46:16.822Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:46:16.822Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.822Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.822Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:46:16.822Z] Suite Duration: 2206ms
[2025-06-21T10:46:16.822Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:46:16.830Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:16.830Z]   [Act] - Step: Executing test logic for "should have retry configuration or error handling"
[2025-06-21T10:46:16.830Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:16.830Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:16.830Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:16.830Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:16.830Z]   [CASE END] - Duration: 102ms
[2025-06-21T10:46:16.831Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T10:46:16.831Z]   Module: whatsapp
[2025-06-21T10:46:16.831Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T10:46:16.906Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T10:46:16.906Z]   Module: order
[2025-06-21T10:46:16.906Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T10:46:16.906Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.906Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.906Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T10:46:16.906Z]   Module: order
[2025-06-21T10:46:16.906Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T10:46:16.906Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.906Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.906Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T10:46:16.906Z]   Module: order
[2025-06-21T10:46:16.906Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T10:46:16.906Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.906Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.906Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T10:46:16.906Z]   Module: order
[2025-06-21T10:46:16.907Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T10:46:16.907Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.907Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.907Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T10:46:16.907Z]   Module: order
[2025-06-21T10:46:16.907Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T10:46:16.907Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.907Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.907Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:46:16.907Z] Suite Duration: 2307ms
[2025-06-21T10:46:16.907Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:46:16.981Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T10:46:16.982Z]   Module: session
[2025-06-21T10:46:16.982Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T10:46:16.982Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.982Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.982Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T10:46:16.982Z]   Module: session
[2025-06-21T10:46:16.982Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T10:46:16.982Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.982Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.982Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T10:46:16.982Z]   Module: session
[2025-06-21T10:46:16.982Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T10:46:16.982Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.982Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.982Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T10:46:16.982Z]   Module: session
[2025-06-21T10:46:16.982Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T10:46:16.982Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.982Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.982Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T10:46:16.982Z]   Module: session
[2025-06-21T10:46:16.982Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T10:46:16.982Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.982Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.982Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T10:46:16.982Z]   Module: session
[2025-06-21T10:46:16.982Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T10:46:16.982Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:16.982Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:16.982Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:46:16.982Z] Suite Duration: 2361ms
[2025-06-21T10:46:16.982Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:46:17.076Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:17.076Z]   [Act] - Step: Executing test logic for "should be able to call getAccessToken method"
[2025-06-21T10:46:17.076Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:17.076Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:17.076Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:17.076Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:17.076Z]   [CASE END] - Duration: 245ms
[2025-06-21T10:46:17.076Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T10:46:17.076Z]   Module: whatsapp
[2025-06-21T10:46:17.076Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T10:46:17.117Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:17.117Z]   [Act] - Step: Executing test logic for "should be able to call sendBasicText method"
[2025-06-21T10:46:17.117Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:17.117Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:17.117Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:17.117Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:17.118Z]   [CASE END] - Duration: 42ms
[2025-06-21T10:46:17.118Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T10:46:17.118Z]   Module: whatsapp
[2025-06-21T10:46:17.118Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T10:46:17.152Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:17.152Z]   [Act] - Step: Executing test logic for "should be able to call sendQuickReply method"
[2025-06-21T10:46:17.152Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:17.152Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:17.152Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:17.152Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:17.152Z]   [CASE END] - Duration: 34ms
[2025-06-21T10:46:17.155Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:46:17.155Z] Suite Duration: 2492ms
[2025-06-21T10:46:17.155Z] Suite Results: 8 passed, 0 failed, 0 skipped
[2025-06-21T10:46:17.378Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T10:46:17.378Z]   Module: dialog
[2025-06-21T10:46:17.378Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T10:46:17.378Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:17.378Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:17.378Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T10:46:17.378Z]   Module: dialog
[2025-06-21T10:46:17.378Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T10:46:17.378Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:17.378Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:17.378Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T10:46:17.378Z]   Module: dialog
[2025-06-21T10:46:17.378Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T10:46:17.378Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:17.378Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:17.378Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T10:46:17.378Z]   Module: dialog
[2025-06-21T10:46:17.378Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T10:46:17.378Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:17.378Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:17.378Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T10:46:17.378Z]   Module: dialog
[2025-06-21T10:46:17.378Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T10:46:17.378Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:17.378Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:17.378Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T10:46:17.378Z]   Module: dialog
[2025-06-21T10:46:17.379Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T10:46:17.379Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:17.379Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:17.379Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:46:17.379Z] Suite Duration: 2767ms
[2025-06-21T10:46:17.379Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:46:17.444Z] 
================================================================================
[2025-06-21T10:46:17.444Z] Test Run Finished
[2025-06-21T10:46:17.444Z] End Timestamp: 2025-06-21T10:46:17.444Z
[2025-06-21T10:46:17.445Z] Total Duration: 3145ms (3.15s)
[2025-06-21T10:46:17.445Z] 
[STATISTICS]
[2025-06-21T10:46:17.445Z] Total Test Suites: 12
[2025-06-21T10:46:17.445Z] Passed Test Suites: 1
[2025-06-21T10:46:17.445Z] Failed Test Suites: 0
[2025-06-21T10:46:17.445Z] Total Tests: 94
[2025-06-21T10:46:17.445Z] Passed Tests: 8
[2025-06-21T10:46:17.445Z] Failed Tests: 0
[2025-06-21T10:46:17.445Z] Skipped Tests: 86
[2025-06-21T10:46:17.445Z] Success Rate: 8.51%
[2025-06-21T10:46:17.445Z] Overall Result: FAILURE
[2025-06-21T10:46:17.445Z] ================================================================================
