[2025-06-21T10:45:53.033Z] ================================================================================
[2025-06-21T10:45:53.033Z] Test Run Started
[2025-06-21T10:45:53.033Z] Timestamp: 2025-06-21T10:45:53.033Z
[2025-06-21T10:45:53.033Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:45:53.033Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:45:53.034Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:45:53.034Z] ================================================================================
[2025-06-21T10:45:53.039Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:45:53.039Z] Total Test Suites: 12
[2025-06-21T10:45:53.039Z] Test Environment: test
[2025-06-21T10:45:53.119Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.120Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.120Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.120Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.120Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.120Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.120Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:45:53.120Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.121Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:45:53.121Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.121Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:45:53.121Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.121Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:45:53.121Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.121Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:45:53.121Z] Suite Display Name: UNIT
[2025-06-21T10:45:53.121Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:45:53.121Z] Suite Display Name: UNIT
[2025-06-21T10:45:55.085Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T10:45:55.086Z]   Module: linkgenerator
[2025-06-21T10:45:55.086Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T10:45:55.087Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.088Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.088Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T10:45:55.088Z]   Module: linkgenerator
[2025-06-21T10:45:55.088Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T10:45:55.089Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.090Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.090Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T10:45:55.090Z]   Module: linkgenerator
[2025-06-21T10:45:55.090Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T10:45:55.090Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.091Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.091Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T10:45:55.091Z]   Module: linkgenerator
[2025-06-21T10:45:55.091Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T10:45:55.091Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.091Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.091Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T10:45:55.091Z]   Module: linkgenerator
[2025-06-21T10:45:55.091Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T10:45:55.091Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.092Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.092Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T10:45:55.092Z]   Module: linkgenerator
[2025-06-21T10:45:55.092Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T10:45:55.092Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.092Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.092Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T10:45:55.092Z] Suite Duration: 1700ms
[2025-06-21T10:45:55.092Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T10:45:55.094Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T10:45:55.094Z]   Module: restaurantstore
[2025-06-21T10:45:55.094Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T10:45:55.094Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.095Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.095Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T10:45:55.095Z]   Module: restaurantstore
[2025-06-21T10:45:55.095Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T10:45:55.095Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.095Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.095Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T10:45:55.095Z]   Module: restaurantstore
[2025-06-21T10:45:55.095Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T10:45:55.095Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.095Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.095Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T10:45:55.095Z]   Module: restaurantstore
[2025-06-21T10:45:55.095Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T10:45:55.096Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.096Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.096Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T10:45:55.096Z]   Module: restaurantstore
[2025-06-21T10:45:55.096Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T10:45:55.096Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.096Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.096Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T10:45:55.096Z]   Module: restaurantstore
[2025-06-21T10:45:55.096Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T10:45:55.096Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.096Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.096Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T10:45:55.096Z]   Module: restaurantstore
[2025-06-21T10:45:55.096Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T10:45:55.096Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.096Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.097Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T10:45:55.097Z]   Module: restaurantstore
[2025-06-21T10:45:55.097Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T10:45:55.097Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.097Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.097Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T10:45:55.097Z]   Module: restaurantstore
[2025-06-21T10:45:55.097Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T10:45:55.097Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.097Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.097Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T10:45:55.097Z] Suite Duration: 1695ms
[2025-06-21T10:45:55.097Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:45:55.136Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T10:45:55.136Z]   Module: sessionservice
[2025-06-21T10:45:55.137Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T10:45:55.137Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.137Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.137Z] 
  [CASE START] - ***********: should have required methods
[2025-06-21T10:45:55.137Z]   Module: sessionservice
[2025-06-21T10:45:55.137Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T10:45:55.137Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.137Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.137Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T10:45:55.137Z]   Module: sessionservice
[2025-06-21T10:45:55.137Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T10:45:55.137Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.137Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.137Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T10:45:55.137Z]   Module: sessionservice
[2025-06-21T10:45:55.137Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T10:45:55.137Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.137Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.137Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T10:45:55.137Z]   Module: sessionservice
[2025-06-21T10:45:55.137Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T10:45:55.137Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.138Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.138Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T10:45:55.138Z]   Module: sessionservice
[2025-06-21T10:45:55.138Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T10:45:55.138Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.138Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.138Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T10:45:55.138Z]   Module: sessionservice
[2025-06-21T10:45:55.138Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T10:45:55.138Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.138Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.138Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T10:45:55.138Z]   Module: sessionservice
[2025-06-21T10:45:55.138Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T10:45:55.138Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.138Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.138Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T10:45:55.138Z]   Module: sessionservice
[2025-06-21T10:45:55.138Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T10:45:55.138Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.138Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.138Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T10:45:55.139Z]   Module: sessionservice
[2025-06-21T10:45:55.139Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T10:45:55.139Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.139Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.139Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T10:45:55.139Z]   Module: sessionservice
[2025-06-21T10:45:55.139Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T10:45:55.139Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.139Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.139Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T10:45:55.139Z] Suite Duration: 1792ms
[2025-06-21T10:45:55.139Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T10:45:55.170Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T10:45:55.171Z]   Module: messagebuilders
[2025-06-21T10:45:55.171Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T10:45:55.171Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.171Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.171Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T10:45:55.171Z]   Module: messagebuilders
[2025-06-21T10:45:55.171Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T10:45:55.171Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.171Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.171Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T10:45:55.171Z]   Module: messagebuilders
[2025-06-21T10:45:55.171Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T10:45:55.171Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.171Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.171Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T10:45:55.171Z]   Module: messagebuilders
[2025-06-21T10:45:55.171Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T10:45:55.172Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.172Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.172Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T10:45:55.172Z]   Module: messagebuilders
[2025-06-21T10:45:55.172Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T10:45:55.172Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.172Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.172Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T10:45:55.172Z]   Module: messagebuilders
[2025-06-21T10:45:55.172Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T10:45:55.172Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.172Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.172Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T10:45:55.172Z]   Module: messagebuilders
[2025-06-21T10:45:55.172Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T10:45:55.172Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.172Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.173Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T10:45:55.173Z]   Module: messagebuilders
[2025-06-21T10:45:55.173Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T10:45:55.173Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.173Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.173Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T10:45:55.173Z]   Module: messagebuilders
[2025-06-21T10:45:55.173Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T10:45:55.173Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.173Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.173Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T10:45:55.173Z]   Module: messagebuilders
[2025-06-21T10:45:55.173Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T10:45:55.173Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.173Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.173Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T10:45:55.173Z]   Module: messagebuilders
[2025-06-21T10:45:55.173Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T10:45:55.174Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.174Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.174Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T10:45:55.174Z]   Module: messagebuilders
[2025-06-21T10:45:55.174Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T10:45:55.174Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.174Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.174Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T10:45:55.174Z]   Module: messagebuilders
[2025-06-21T10:45:55.174Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T10:45:55.174Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.174Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.174Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T10:45:55.174Z]   Module: messagebuilders
[2025-06-21T10:45:55.174Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T10:45:55.174Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.174Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.174Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T10:45:55.174Z]   Module: messagebuilders
[2025-06-21T10:45:55.174Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T10:45:55.174Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.174Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.175Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T10:45:55.175Z]   Module: messagebuilders
[2025-06-21T10:45:55.175Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T10:45:55.175Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.175Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.175Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T10:45:55.175Z] Suite Duration: 1828ms
[2025-06-21T10:45:55.175Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T10:45:55.222Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T10:45:55.222Z]   Module: whatsapp
[2025-06-21T10:45:55.222Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T10:45:55.325Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T10:45:55.326Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.326Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T10:45:55.326Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.326Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.326Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T10:45:55.326Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.326Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T10:45:55.326Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.326Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.326Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T10:45:55.326Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.326Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T10:45:55.326Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.326Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.326Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T10:45:55.326Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.327Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T10:45:55.327Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.327Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.327Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T10:45:55.327Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.327Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T10:45:55.327Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.327Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.327Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T10:45:55.327Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.327Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T10:45:55.327Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.327Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.327Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T10:45:55.327Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.327Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T10:45:55.327Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.327Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.327Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T10:45:55.327Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.328Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T10:45:55.328Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.328Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.328Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T10:45:55.328Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.328Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T10:45:55.328Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.328Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.328Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T10:45:55.328Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.328Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T10:45:55.328Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.328Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.328Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T10:45:55.329Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.329Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T10:45:55.329Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.329Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.330Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T10:45:55.330Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.330Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T10:45:55.330Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.330Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.330Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T10:45:55.330Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.330Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T10:45:55.331Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.331Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.331Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T10:45:55.331Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.331Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T10:45:55.331Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.331Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.331Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T10:45:55.331Z]   Module: sessionidgenerator
[2025-06-21T10:45:55.331Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T10:45:55.331Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.331Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.331Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T10:45:55.331Z] Suite Duration: 1964ms
[2025-06-21T10:45:55.331Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T10:45:55.520Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:45:55.520Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T10:45:55.520Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:45:55.520Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:45:55.520Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:45:55.520Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:45:55.520Z]   [CASE END] - Duration: 302ms
[2025-06-21T10:45:55.531Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T10:45:55.531Z]   Module: whatsapp
[2025-06-21T10:45:55.531Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T10:45:55.531Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.531Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.531Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T10:45:55.531Z]   Module: whatsapp
[2025-06-21T10:45:55.531Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T10:45:55.531Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.532Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.532Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T10:45:55.532Z]   Module: whatsapp
[2025-06-21T10:45:55.532Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T10:45:55.532Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.532Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.532Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T10:45:55.532Z]   Module: whatsapp
[2025-06-21T10:45:55.532Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T10:45:55.532Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.532Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.532Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T10:45:55.533Z]   Module: whatsapp
[2025-06-21T10:45:55.533Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T10:45:55.533Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.533Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.533Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T10:45:55.533Z]   Module: whatsapp
[2025-06-21T10:45:55.533Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T10:45:55.533Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.533Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.533Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T10:45:55.533Z]   Module: whatsapp
[2025-06-21T10:45:55.533Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T10:45:55.533Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.533Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.533Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T10:45:55.533Z] Suite Duration: 2130ms
[2025-06-21T10:45:55.533Z] Suite Results: 1 passed, 0 failed, 7 skipped
[2025-06-21T10:45:55.713Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:45:55.714Z]   Module: order
[2025-06-21T10:45:55.714Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T10:45:55.714Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.714Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.714Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:45:55.714Z]   Module: order
[2025-06-21T10:45:55.714Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T10:45:55.714Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.714Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.714Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:45:55.714Z]   Module: order
[2025-06-21T10:45:55.714Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T10:45:55.714Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.714Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.714Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T10:45:55.714Z] Suite Duration: 2373ms
[2025-06-21T10:45:55.714Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:45:55.738Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T10:45:55.738Z]   Module: order
[2025-06-21T10:45:55.738Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T10:45:55.738Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.738Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.738Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T10:45:55.738Z]   Module: order
[2025-06-21T10:45:55.738Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T10:45:55.738Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.738Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.738Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T10:45:55.738Z]   Module: order
[2025-06-21T10:45:55.738Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T10:45:55.738Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.738Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.739Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T10:45:55.739Z]   Module: order
[2025-06-21T10:45:55.739Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T10:45:55.739Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.739Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.739Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:45:55.739Z]   Module: order
[2025-06-21T10:45:55.739Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T10:45:55.739Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.739Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.739Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T10:45:55.739Z] Suite Duration: 2392ms
[2025-06-21T10:45:55.739Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:45:55.825Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T10:45:55.825Z]   Module: order
[2025-06-21T10:45:55.825Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T10:45:55.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.825Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T10:45:55.825Z]   Module: order
[2025-06-21T10:45:55.825Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T10:45:55.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.825Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T10:45:55.825Z]   Module: order
[2025-06-21T10:45:55.825Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T10:45:55.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.825Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T10:45:55.825Z]   Module: order
[2025-06-21T10:45:55.825Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T10:45:55.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.826Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T10:45:55.826Z] Suite Duration: 2468ms
[2025-06-21T10:45:55.826Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:45:55.828Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T10:45:55.828Z]   Module: order
[2025-06-21T10:45:55.828Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T10:45:55.828Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.828Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.828Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T10:45:55.829Z]   Module: order
[2025-06-21T10:45:55.829Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T10:45:55.829Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.829Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.829Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T10:45:55.829Z]   Module: order
[2025-06-21T10:45:55.829Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T10:45:55.829Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.829Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.829Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T10:45:55.829Z]   Module: order
[2025-06-21T10:45:55.829Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T10:45:55.829Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.829Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.829Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T10:45:55.829Z]   Module: order
[2025-06-21T10:45:55.829Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T10:45:55.829Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.829Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.829Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T10:45:55.829Z] Suite Duration: 2486ms
[2025-06-21T10:45:55.829Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T10:45:55.893Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T10:45:55.894Z]   Module: session
[2025-06-21T10:45:55.894Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T10:45:55.894Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.894Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.894Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T10:45:55.894Z]   Module: session
[2025-06-21T10:45:55.894Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T10:45:55.894Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.894Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.894Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T10:45:55.894Z]   Module: session
[2025-06-21T10:45:55.894Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T10:45:55.894Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.894Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.894Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T10:45:55.894Z]   Module: session
[2025-06-21T10:45:55.894Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T10:45:55.894Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.894Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.894Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T10:45:55.894Z]   Module: session
[2025-06-21T10:45:55.894Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T10:45:55.894Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.894Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.894Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T10:45:55.894Z]   Module: session
[2025-06-21T10:45:55.894Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T10:45:55.894Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:45:55.894Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:45:55.894Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T10:45:55.894Z] Suite Duration: 2533ms
[2025-06-21T10:45:55.894Z] Suite Results: 0 passed, 0 failed, 6 skipped
