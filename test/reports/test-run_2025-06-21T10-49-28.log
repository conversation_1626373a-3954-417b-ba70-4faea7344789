[2025-06-21T10:49:28.387Z] ================================================================================
[2025-06-21T10:49:28.387Z] Test Run Started
[2025-06-21T10:49:28.387Z] Timestamp: 2025-06-21T10:49:28.387Z
[2025-06-21T10:49:28.387Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:49:28.388Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:49:28.388Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:49:28.388Z] ================================================================================
[2025-06-21T10:49:28.390Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:49:28.390Z] Total Test Suites: 14
[2025-06-21T10:49:28.390Z] Test Environment: test
[2025-06-21T10:49:28.395Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:49:28.395Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:30.671Z] 
  [CASE START] - TC-674469B3: should process incoming message and create session
[2025-06-21T10:49:30.672Z]   Module: whatsapp
[2025-06-21T10:49:30.672Z]   Full Path: WhatsApp Webhook Integration › should process incoming message and create session
[2025-06-21T10:49:30.672Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:30.672Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:30.672Z] 
  [CASE START] - TC-432D970F: should reject webhook with invalid signature
[2025-06-21T10:49:30.672Z]   Module: whatsapp
[2025-06-21T10:49:30.672Z]   Full Path: WhatsApp Webhook Integration › should reject webhook with invalid signature
[2025-06-21T10:49:30.672Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:30.672Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:30.672Z] 
  [CASE START] - TC-3376EAD8: should handle malformed webhook payload
[2025-06-21T10:49:30.672Z]   Module: whatsapp
[2025-06-21T10:49:30.672Z]   Full Path: WhatsApp Webhook Integration › should handle malformed webhook payload
[2025-06-21T10:49:30.672Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:30.672Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:30.672Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:49:30.672Z] Suite Duration: 2260ms
[2025-06-21T10:49:30.672Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:49:30.673Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:49:30.673Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:30.817Z] 
  [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-21T10:49:30.817Z]   Module: refund
[2025-06-21T10:49:30.818Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-21T10:49:30.818Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:30.818Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:30.818Z] 
  [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-21T10:49:30.818Z]   Module: refund
[2025-06-21T10:49:30.818Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-21T10:49:30.818Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:30.818Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:30.818Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:49:30.818Z] Suite Duration: 139ms
[2025-06-21T10:49:30.818Z] Suite Results: 0 passed, 0 failed, 2 skipped
[2025-06-21T10:49:30.818Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:49:30.818Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:31.878Z] 
  [CASE START] - TC-2E236918: should create real payment intent with Stripe
[2025-06-21T10:49:31.878Z]   Module: real
[2025-06-21T10:49:31.878Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should create real payment intent with Stripe
[2025-06-21T10:49:31.878Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.878Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.878Z] 
  [CASE START] - TC-18AE2E8F: should retrieve real payment intent status
[2025-06-21T10:49:31.878Z]   Module: real
[2025-06-21T10:49:31.878Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should retrieve real payment intent status
[2025-06-21T10:49:31.878Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.878Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.878Z] 
  [CASE START] - TC-6AADE1B7: should handle payment with test card
[2025-06-21T10:49:31.878Z]   Module: real
[2025-06-21T10:49:31.878Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle payment with test card
[2025-06-21T10:49:31.878Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.878Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.878Z] 
  [CASE START] - TC-54B75689: should handle declined card
[2025-06-21T10:49:31.878Z]   Module: real
[2025-06-21T10:49:31.878Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle declined card
[2025-06-21T10:49:31.878Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.878Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.878Z] 
  [CASE START] - TC-332A1C6D: should create real Stripe customer
[2025-06-21T10:49:31.879Z]   Module: real
[2025-06-21T10:49:31.879Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should create real Stripe customer
[2025-06-21T10:49:31.879Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.879Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.879Z] 
  [CASE START] - TC-164683AD: should retrieve real Stripe customer
[2025-06-21T10:49:31.879Z]   Module: real
[2025-06-21T10:49:31.879Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should retrieve real Stripe customer
[2025-06-21T10:49:31.879Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.879Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.879Z] 
  [CASE START] - TC-5FD44F7E: should process real webhook signature validation
[2025-06-21T10:49:31.879Z]   Module: real
[2025-06-21T10:49:31.879Z]   Full Path: Real Stripe Payment Integration Tests › Real Webhook Processing › should process real webhook signature validation
[2025-06-21T10:49:31.879Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.879Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.879Z] 
  [CASE START] - TC-74FBC87B: should handle invalid amount
[2025-06-21T10:49:31.879Z]   Module: real
[2025-06-21T10:49:31.879Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid amount
[2025-06-21T10:49:31.879Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.879Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.879Z] 
  [CASE START] - TC-56B8BFFE: should handle invalid currency
[2025-06-21T10:49:31.879Z]   Module: real
[2025-06-21T10:49:31.879Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid currency
[2025-06-21T10:49:31.879Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:31.879Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:31.879Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:49:31.879Z] Suite Duration: 1056ms
[2025-06-21T10:49:31.879Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:49:31.879Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:49:31.879Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:32.646Z] 
  [CASE START] - TC-3CAE5E97: should create a new order
[2025-06-21T10:49:32.646Z]   Module: graphql
[2025-06-21T10:49:32.646Z]   Full Path: Order GraphQL Mutations › should create a new order
[2025-06-21T10:49:32.647Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:32.647Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:32.647Z] 
  [CASE START] - TC-6385F57E: should fail to create order with invalid restaurant ID
[2025-06-21T10:49:32.647Z]   Module: graphql
[2025-06-21T10:49:32.647Z]   Full Path: Order GraphQL Mutations › should fail to create order with invalid restaurant ID
[2025-06-21T10:49:32.647Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:32.647Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:32.647Z] 
  [CASE START] - TC-7D51EFC9: should fail to create order without authentication
[2025-06-21T10:49:32.647Z]   Module: graphql
[2025-06-21T10:49:32.647Z]   Full Path: Order GraphQL Mutations › should fail to create order without authentication
[2025-06-21T10:49:32.647Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:32.647Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:32.647Z] 
  [CASE START] - TC-7912DF71: should update order status
[2025-06-21T10:49:32.647Z]   Module: graphql
[2025-06-21T10:49:32.647Z]   Full Path: Order GraphQL Mutations › should update order status
[2025-06-21T10:49:32.647Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:32.647Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:32.647Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:49:32.647Z] Suite Duration: 764ms
[2025-06-21T10:49:32.647Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:49:32.647Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:49:32.647Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:33.988Z] 
  [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-21T10:49:33.988Z]   Module: graphql
[2025-06-21T10:49:33.988Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-21T10:49:34.243Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type in schema"
[2025-06-21T10:49:34.244Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-21T10:49:34.244Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:34.244Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:34.244Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:34.244Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:34.244Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:34.244Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:34.244Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:34.244Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:34.244Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:34.244Z]   [CASE END] - Duration: 256ms
[2025-06-21T10:49:34.244Z] 
  [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-21T10:49:34.244Z]   Module: graphql
[2025-06-21T10:49:34.244Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-21T10:49:34.287Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderStatus enum in schema"
[2025-06-21T10:49:34.287Z]   [Act] - Step: Executing test logic for "should have OrderStatus enum in schema"
[2025-06-21T10:49:34.287Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:34.287Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:34.287Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:34.287Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:34.287Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:34.287Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:34.287Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:34.287Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:34.287Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:34.287Z]   [CASE END] - Duration: 43ms
[2025-06-21T10:49:34.287Z] 
  [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-21T10:49:34.287Z]   Module: graphql
[2025-06-21T10:49:34.287Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-21T10:49:34.316Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderInput type in schema"
[2025-06-21T10:49:34.316Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-21T10:49:34.316Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:34.316Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:34.316Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:34.316Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:34.316Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:34.316Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:34.316Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:34.316Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:34.316Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:34.316Z]   [CASE END] - Duration: 29ms
[2025-06-21T10:49:34.318Z] 
  [CASE START] - TC-1EE21EDD: should validate GraphQL order operations
[2025-06-21T10:49:34.318Z]   Module: graphql
[2025-06-21T10:49:34.318Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should validate GraphQL order operations
[2025-06-21T10:49:34.318Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:34.318Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:34.318Z] 
  [CASE START] - TC-8227B16: should handle malformed order queries
[2025-06-21T10:49:34.318Z]   Module: graphql
[2025-06-21T10:49:34.318Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should handle malformed order queries
[2025-06-21T10:49:34.318Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:34.318Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:34.318Z] 
  [CASE START] - TC-C2E23B4: should validate required arguments
[2025-06-21T10:49:34.318Z]   Module: graphql
[2025-06-21T10:49:34.318Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should validate required arguments
[2025-06-21T10:49:34.319Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:34.319Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:34.319Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:49:34.319Z] Suite Duration: 1667ms
[2025-06-21T10:49:34.319Z] Suite Results: 3 passed, 0 failed, 3 skipped
[2025-06-21T10:49:34.319Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:49:34.319Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:35.627Z] 
  [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-21T10:49:35.627Z]   Module: graphql
[2025-06-21T10:49:35.627Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-21T10:49:35.683Z]   [Arrange] - Precondition: Test environment prepared for "should respond to GraphQL introspection query"
[2025-06-21T10:49:35.683Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-21T10:49:35.683Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:35.683Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:35.683Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:35.683Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:35.683Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:35.683Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:35.683Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:35.683Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:35.683Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:35.683Z]   [CASE END] - Duration: 56ms
[2025-06-21T10:49:35.683Z] 
  [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-21T10:49:35.683Z]   Module: graphql
[2025-06-21T10:49:35.683Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-21T10:49:35.708Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T10:49:35.708Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:49:35.708Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:35.708Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:35.708Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:35.708Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:35.708Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:35.708Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:35.708Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:35.708Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:35.708Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:35.708Z]   [CASE END] - Duration: 25ms
[2025-06-21T10:49:35.709Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:49:35.709Z] Suite Duration: 1388ms
[2025-06-21T10:49:35.709Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:49:35.709Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:49:35.709Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:36.398Z] 
  [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-21T10:49:36.398Z]   Module: graphql
[2025-06-21T10:49:36.398Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-21T10:49:36.448Z]   [Arrange] - Precondition: Test environment prepared for "should have Customer type in schema"
[2025-06-21T10:49:36.448Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-21T10:49:36.448Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:36.448Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:36.448Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:36.448Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:36.448Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:36.448Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:36.448Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:36.448Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:36.448Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:36.448Z]   [CASE END] - Duration: 50ms
[2025-06-21T10:49:36.448Z] 
  [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-21T10:49:36.448Z]   Module: graphql
[2025-06-21T10:49:36.448Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-21T10:49:36.472Z]   [Arrange] - Precondition: Test environment prepared for "should have Address type in schema"
[2025-06-21T10:49:36.472Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-21T10:49:36.472Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:36.472Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:36.472Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:36.473Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:36.473Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:36.473Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:36.473Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:36.473Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:36.473Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:36.473Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:49:36.473Z] 
  [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-21T10:49:36.473Z]   Module: graphql
[2025-06-21T10:49:36.473Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-21T10:49:36.495Z]   [Arrange] - Precondition: Test environment prepared for "should have AddressInput type in schema"
[2025-06-21T10:49:36.495Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-21T10:49:36.495Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:36.495Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:36.495Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:36.495Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:36.495Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:36.495Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:36.495Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:36.495Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:36.495Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:36.495Z]   [CASE END] - Duration: 22ms
[2025-06-21T10:49:36.496Z] 
  [CASE START] - TC-2C17DA72: should validate customer-related mutations exist
[2025-06-21T10:49:36.496Z]   Module: graphql
[2025-06-21T10:49:36.496Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should validate customer-related mutations exist
[2025-06-21T10:49:36.496Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:36.496Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:36.496Z] 
  [CASE START] - TC-D206B8: should handle GraphQL validation errors
[2025-06-21T10:49:36.497Z]   Module: graphql
[2025-06-21T10:49:36.497Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should handle GraphQL validation errors
[2025-06-21T10:49:36.497Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:36.497Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:36.497Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:49:36.497Z] Suite Duration: 784ms
[2025-06-21T10:49:36.497Z] Suite Results: 3 passed, 0 failed, 2 skipped
[2025-06-21T10:49:36.497Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:49:36.497Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:37.229Z] 
  [CASE START] - ***********: should have updateOrderStatus mutation in schema
[2025-06-21T10:49:37.229Z]   Module: graphql
[2025-06-21T10:49:37.229Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-21T10:49:37.282Z]   [Arrange] - Precondition: Test environment prepared for "should have updateOrderStatus mutation in schema"
[2025-06-21T10:49:37.282Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-21T10:49:37.282Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:37.282Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:37.282Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:37.282Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:37.282Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:37.282Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:37.283Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:37.283Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:37.283Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:37.283Z]   [CASE END] - Duration: 54ms
[2025-06-21T10:49:37.283Z] 
  [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-21T10:49:37.283Z]   Module: graphql
[2025-06-21T10:49:37.283Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-21T10:49:37.309Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type with orderStatus field in schema"
[2025-06-21T10:49:37.309Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-21T10:49:37.309Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:37.309Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:37.309Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:37.309Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:37.309Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:37.309Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:37.309Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:37.309Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:37.309Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:37.309Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:49:37.309Z] 
  [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-21T10:49:37.309Z]   Module: graphql
[2025-06-21T10:49:37.309Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-21T10:49:37.337Z]   [Arrange] - Precondition: Test environment prepared for "should have order status enum values in schema"
[2025-06-21T10:49:37.337Z]   [Act] - Step: Executing test logic for "should have order status enum values in schema"
[2025-06-21T10:49:37.337Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:37.337Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:37.337Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:37.337Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:37.337Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:37.337Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:37.337Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:37.337Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:37.337Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:37.337Z]   [CASE END] - Duration: 28ms
[2025-06-21T10:49:37.339Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:49:37.339Z] Suite Duration: 839ms
[2025-06-21T10:49:37.339Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-21T10:49:37.339Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:49:37.339Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:38.050Z] 
  [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-21T10:49:38.050Z]   Module: graphql
[2025-06-21T10:49:38.050Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-21T10:49:38.105Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T10:49:38.105Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:49:38.105Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:38.105Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:38.105Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:38.105Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:38.105Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:38.105Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:38.105Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:38.105Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:38.105Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:38.105Z]   [CASE END] - Duration: 54ms
[2025-06-21T10:49:38.105Z] 
  [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-21T10:49:38.105Z]   Module: graphql
[2025-06-21T10:49:38.105Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-21T10:49:38.129Z]   [Arrange] - Precondition: Test environment prepared for "should support schema introspection"
[2025-06-21T10:49:38.129Z]   [Act] - Step: Executing test logic for "should support schema introspection"
[2025-06-21T10:49:38.129Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:38.129Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:38.129Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:38.129Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:38.129Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:38.129Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:38.129Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:38.129Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:38.129Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:38.130Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:49:38.130Z] 
  [CASE START] - TC-6C7A3797: should list available queries
[2025-06-21T10:49:38.130Z]   Module: graphql
[2025-06-21T10:49:38.130Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-21T10:49:38.152Z]   [Arrange] - Precondition: Test environment prepared for "should list available queries"
[2025-06-21T10:49:38.152Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-21T10:49:38.152Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:38.152Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:38.152Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:38.152Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:38.152Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:38.152Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:38.152Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:38.152Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:38.152Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:38.152Z]   [CASE END] - Duration: 22ms
[2025-06-21T10:49:38.152Z] 
  [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-21T10:49:38.152Z]   Module: graphql
[2025-06-21T10:49:38.152Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-21T10:49:38.178Z]   [Arrange] - Precondition: Test environment prepared for "should list available mutations"
[2025-06-21T10:49:38.178Z]   [Act] - Step: Executing test logic for "should list available mutations"
[2025-06-21T10:49:38.178Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:38.178Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:38.178Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:38.178Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:38.178Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:38.178Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:38.178Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:38.178Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:38.178Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:38.178Z]   [CASE END] - Duration: 25ms
[2025-06-21T10:49:38.179Z] 
  [CASE START] - TC-387C915A: should respond to GraphQL endpoint
[2025-06-21T10:49:38.179Z]   Module: graphql
[2025-06-21T10:49:38.179Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should respond to GraphQL endpoint
[2025-06-21T10:49:38.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:38.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:38.179Z] 
  [CASE START] - TC-21545EE6: should handle invalid GraphQL queries
[2025-06-21T10:49:38.179Z]   Module: graphql
[2025-06-21T10:49:38.179Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should handle invalid GraphQL queries
[2025-06-21T10:49:38.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:38.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:38.179Z] 
  [CASE START] - TC-18BF1AAD: should handle simple queries
[2025-06-21T10:49:38.179Z]   Module: graphql
[2025-06-21T10:49:38.179Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle simple queries
[2025-06-21T10:49:38.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:38.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:38.179Z] 
  [CASE START] - TC-14898FE1: should validate GraphQL syntax
[2025-06-21T10:49:38.179Z]   Module: graphql
[2025-06-21T10:49:38.179Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should validate GraphQL syntax
[2025-06-21T10:49:38.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:38.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:38.180Z] 
  [CASE START] - TC-63BF36C2: should handle empty queries
[2025-06-21T10:49:38.180Z]   Module: graphql
[2025-06-21T10:49:38.180Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle empty queries
[2025-06-21T10:49:38.180Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:49:38.180Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:49:38.180Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:49:38.180Z] Suite Duration: 835ms
[2025-06-21T10:49:38.180Z] Suite Results: 4 passed, 0 failed, 5 skipped
[2025-06-21T10:49:38.180Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:49:38.180Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:39.021Z] 
  [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-21T10:49:39.021Z]   Module: graphql
[2025-06-21T10:49:39.021Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:49:39.096Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T10:49:39.096Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:49:39.096Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:39.096Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:39.096Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:39.096Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:39.096Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:39.096Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:39.096Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:39.096Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:39.096Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:39.096Z]   [CASE END] - Duration: 75ms
[2025-06-21T10:49:39.097Z] 
  [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-21T10:49:39.097Z]   Module: graphql
[2025-06-21T10:49:39.097Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-21T10:49:39.130Z]   [Arrange] - Precondition: Test environment prepared for "should have query types in schema"
[2025-06-21T10:49:39.130Z]   [Act] - Step: Executing test logic for "should have query types in schema"
[2025-06-21T10:49:39.130Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:39.130Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:39.130Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:39.130Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:39.130Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:39.130Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:39.130Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:39.130Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:39.130Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:39.130Z]   [CASE END] - Duration: 33ms
[2025-06-21T10:49:39.131Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:49:39.131Z] Suite Duration: 949ms
[2025-06-21T10:49:39.131Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:49:39.132Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:49:39.132Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:39.866Z] 
  [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-21T10:49:39.866Z]   Module: order
[2025-06-21T10:49:39.866Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-21T10:49:39.924Z]   [Arrange] - Precondition: Test environment prepared for "should have order query in schema"
[2025-06-21T10:49:39.924Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-21T10:49:39.924Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:39.924Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:39.924Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:39.924Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:39.924Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:39.925Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:39.925Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:39.925Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:39.925Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:39.925Z]   [CASE END] - Duration: 59ms
[2025-06-21T10:49:39.925Z] 
  [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-21T10:49:39.925Z]   Module: order
[2025-06-21T10:49:39.925Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-21T10:49:39.955Z]   [Arrange] - Precondition: Test environment prepared for "should have orders query in schema"
[2025-06-21T10:49:39.955Z]   [Act] - Step: Executing test logic for "should have orders query in schema"
[2025-06-21T10:49:39.955Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:39.955Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:39.955Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:39.955Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:39.955Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:39.955Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:39.955Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:39.955Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:39.955Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:39.955Z]   [CASE END] - Duration: 29ms
[2025-06-21T10:49:39.956Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:49:39.956Z] Suite Duration: 821ms
[2025-06-21T10:49:39.956Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:49:39.956Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:49:39.956Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:40.759Z] 
  [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-21T10:49:40.760Z]   Module: graphql
[2025-06-21T10:49:40.760Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:49:40.826Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T10:49:40.826Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:49:40.826Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:40.826Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:40.826Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:40.826Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:40.826Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:40.826Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:40.826Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:40.826Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:40.827Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:40.827Z]   [CASE END] - Duration: 67ms
[2025-06-21T10:49:40.827Z] 
  [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-21T10:49:40.827Z]   Module: graphql
[2025-06-21T10:49:40.827Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-21T10:49:40.851Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T10:49:40.851Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:49:40.851Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:40.851Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:40.851Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:40.851Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:40.851Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:40.851Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:40.851Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:40.851Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:40.851Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:40.851Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:49:40.852Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:49:40.852Z] Suite Duration: 893ms
[2025-06-21T10:49:40.852Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:49:40.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:49:40.852Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:41.545Z] 
  [CASE START] - TC-7DB19ADA: should have order subscription in schema
[2025-06-21T10:49:41.545Z]   Module: graphql
[2025-06-21T10:49:41.545Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have order subscription in schema
[2025-06-21T10:49:41.598Z]   [Arrange] - Precondition: Test environment prepared for "should have order subscription in schema"
[2025-06-21T10:49:41.598Z]   [Act] - Step: Executing test logic for "should have order subscription in schema"
[2025-06-21T10:49:41.598Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:41.598Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:41.598Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:41.598Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:41.598Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:41.598Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:41.598Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:41.598Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:41.598Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:41.598Z]   [CASE END] - Duration: 53ms
[2025-06-21T10:49:41.598Z] 
  [CASE START] - TC-7B992587: should have mutation types in schema
[2025-06-21T10:49:41.598Z]   Module: graphql
[2025-06-21T10:49:41.598Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have mutation types in schema
[2025-06-21T10:49:41.624Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T10:49:41.624Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:49:41.624Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:41.624Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:41.624Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:41.624Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:41.624Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:41.624Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:41.624Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:41.624Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:41.624Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:41.624Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:49:41.625Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:49:41.625Z] Suite Duration: 770ms
[2025-06-21T10:49:41.625Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:49:41.625Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:49:41.626Z] Suite Display Name: INTEGRATION
[2025-06-21T10:49:42.304Z] 
  [CASE START] - TC-528B5677: should have payment related types in schema
[2025-06-21T10:49:42.304Z]   Module: graphql
[2025-06-21T10:49:42.304Z]   Full Path: Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:49:42.358Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T10:49:42.358Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:49:42.358Z]   [Act Log] - Initializing test context and dependencies
[2025-06-21T10:49:42.358Z]   [Act Log] - Setting up mock objects and test data
[2025-06-21T10:49:42.358Z]   [Act Log] - Invoking target method or function
[2025-06-21T10:49:42.358Z]   [Act Log] - Execution completed successfully without errors
[2025-06-21T10:49:42.358Z]   [Act Log] - All method calls returned expected values
[2025-06-21T10:49:42.358Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T10:49:42.358Z]   [Assert Log] - Comparison details: Expected behavior matches actual behavior
[2025-06-21T10:49:42.358Z]   [Assert Log] - All assertions passed: return values, state changes, and side effects verified
[2025-06-21T10:49:42.358Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:49:42.358Z]   [CASE END] - Duration: 54ms
[2025-06-21T10:49:42.359Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:49:42.359Z] Suite Duration: 731ms
[2025-06-21T10:49:42.359Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T10:49:42.368Z] 
================================================================================
[2025-06-21T10:49:42.368Z] Test Run Finished
[2025-06-21T10:49:42.368Z] End Timestamp: 2025-06-21T10:49:42.368Z
[2025-06-21T10:49:42.368Z] Total Duration: 13981ms (13.98s)
[2025-06-21T10:49:42.368Z] 
[STATISTICS]
[2025-06-21T10:49:42.368Z] Total Test Suites: 14
[2025-06-21T10:49:42.368Z] Passed Test Suites: 10
[2025-06-21T10:49:42.368Z] Failed Test Suites: 0
[2025-06-21T10:49:42.368Z] Total Tests: 52
[2025-06-21T10:49:42.368Z] Passed Tests: 24
[2025-06-21T10:49:42.368Z] Failed Tests: 0
[2025-06-21T10:49:42.368Z] Skipped Tests: 28
[2025-06-21T10:49:42.368Z] Success Rate: 46.15%
[2025-06-21T10:49:42.368Z] Overall Result: FAILURE
[2025-06-21T10:49:42.368Z] ================================================================================
