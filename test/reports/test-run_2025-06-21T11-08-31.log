[2025-06-21T11:08:31.916Z] ================================================================================
[2025-06-21T11:08:31.916Z] Test Run Started
[2025-06-21T11:08:31.916Z] Timestamp: 2025-06-21T11:08:31.916Z
[2025-06-21T11:08:31.916Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:08:31.916Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:08:31.916Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:08:31.916Z] ================================================================================
[2025-06-21T11:08:31.920Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:08:31.920Z] Total Test Suites: 12
[2025-06-21T11:08:31.921Z] Test Environment: test
[2025-06-21T11:08:32.002Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:08:32.002Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.002Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:08:32.002Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.002Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:08:32.002Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.002Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:08:32.002Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.002Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:08:32.002Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.002Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.003Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.003Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.003Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.003Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.003Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:32.003Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:08:32.003Z] Suite Display Name: UNIT
[2025-06-21T11:08:33.644Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T11:08:33.645Z]   Module: sessionservice
[2025-06-21T11:08:33.646Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T11:08:33.646Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.646Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.647Z] 
  [CASE START] - TC-55708206: should have required methods
[2025-06-21T11:08:33.647Z]   Module: sessionservice
[2025-06-21T11:08:33.647Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T11:08:33.648Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.648Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.648Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T11:08:33.648Z]   Module: sessionservice
[2025-06-21T11:08:33.649Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T11:08:33.649Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.649Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.649Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T11:08:33.649Z]   Module: sessionservice
[2025-06-21T11:08:33.649Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T11:08:33.649Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.649Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.649Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T11:08:33.649Z]   Module: sessionservice
[2025-06-21T11:08:33.649Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T11:08:33.649Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.649Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.650Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T11:08:33.650Z]   Module: sessionservice
[2025-06-21T11:08:33.650Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T11:08:33.650Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.651Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.651Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T11:08:33.651Z]   Module: sessionservice
[2025-06-21T11:08:33.651Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T11:08:33.651Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.652Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.652Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T11:08:33.652Z]   Module: sessionservice
[2025-06-21T11:08:33.652Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T11:08:33.653Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.653Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.653Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T11:08:33.653Z]   Module: sessionservice
[2025-06-21T11:08:33.653Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T11:08:33.654Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.654Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.654Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T11:08:33.654Z]   Module: sessionservice
[2025-06-21T11:08:33.654Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T11:08:33.654Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.654Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.654Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T11:08:33.654Z]   Module: sessionservice
[2025-06-21T11:08:33.654Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T11:08:33.654Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.654Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.654Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:08:33.654Z] Suite Duration: 1429ms
[2025-06-21T11:08:33.654Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T11:08:33.655Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T11:08:33.655Z]   Module: linkgenerator
[2025-06-21T11:08:33.655Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T11:08:33.655Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.655Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.655Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T11:08:33.655Z]   Module: linkgenerator
[2025-06-21T11:08:33.655Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T11:08:33.656Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.656Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.656Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T11:08:33.656Z]   Module: linkgenerator
[2025-06-21T11:08:33.656Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T11:08:33.656Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.656Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.656Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T11:08:33.656Z]   Module: linkgenerator
[2025-06-21T11:08:33.656Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T11:08:33.656Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.656Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.656Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T11:08:33.656Z]   Module: linkgenerator
[2025-06-21T11:08:33.656Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T11:08:33.656Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.656Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.656Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T11:08:33.656Z]   Module: linkgenerator
[2025-06-21T11:08:33.656Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T11:08:33.657Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.657Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.657Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:08:33.657Z] Suite Duration: 1425ms
[2025-06-21T11:08:33.657Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:08:33.773Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T11:08:33.773Z]   Module: whatsapp
[2025-06-21T11:08:33.773Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T11:08:33.775Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T11:08:33.775Z]   Module: restaurantstore
[2025-06-21T11:08:33.775Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T11:08:33.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.776Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T11:08:33.776Z]   Module: restaurantstore
[2025-06-21T11:08:33.776Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T11:08:33.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.776Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T11:08:33.776Z]   Module: restaurantstore
[2025-06-21T11:08:33.776Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T11:08:33.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.776Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T11:08:33.776Z]   Module: restaurantstore
[2025-06-21T11:08:33.776Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T11:08:33.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.776Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T11:08:33.776Z]   Module: restaurantstore
[2025-06-21T11:08:33.776Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T11:08:33.776Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.776Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.776Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T11:08:33.776Z]   Module: restaurantstore
[2025-06-21T11:08:33.776Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T11:08:33.777Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.777Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.777Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T11:08:33.777Z]   Module: restaurantstore
[2025-06-21T11:08:33.777Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T11:08:33.777Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.777Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.777Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T11:08:33.777Z]   Module: restaurantstore
[2025-06-21T11:08:33.777Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T11:08:33.777Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.777Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.777Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T11:08:33.777Z]   Module: restaurantstore
[2025-06-21T11:08:33.777Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T11:08:33.777Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.777Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.777Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:08:33.777Z] Suite Duration: 1482ms
[2025-06-21T11:08:33.777Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:08:33.823Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T11:08:33.823Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.823Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T11:08:33.823Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.823Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.823Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T11:08:33.823Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.823Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T11:08:33.823Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.823Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.823Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T11:08:33.823Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.824Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T11:08:33.824Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.824Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.824Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T11:08:33.824Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.824Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T11:08:33.824Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.824Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.824Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T11:08:33.824Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.824Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T11:08:33.824Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.824Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.824Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T11:08:33.824Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.824Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T11:08:33.824Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.824Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.824Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T11:08:33.824Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.824Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T11:08:33.824Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.824Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.824Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T11:08:33.824Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.824Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T11:08:33.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.825Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T11:08:33.825Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.825Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T11:08:33.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.825Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T11:08:33.825Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.825Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T11:08:33.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.825Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T11:08:33.825Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.825Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T11:08:33.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.825Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T11:08:33.825Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.825Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T11:08:33.825Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.825Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.825Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T11:08:33.825Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.825Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T11:08:33.826Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.826Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.826Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T11:08:33.826Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.826Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T11:08:33.826Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.826Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.826Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T11:08:33.826Z]   Module: sessionidgenerator
[2025-06-21T11:08:33.826Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T11:08:33.826Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.826Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.826Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:08:33.826Z] Suite Duration: 1630ms
[2025-06-21T11:08:33.826Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T11:08:33.837Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T11:08:33.837Z]   Module: messagebuilders
[2025-06-21T11:08:33.837Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T11:08:33.837Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.837Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.837Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T11:08:33.837Z]   Module: messagebuilders
[2025-06-21T11:08:33.838Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T11:08:33.838Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.838Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.838Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T11:08:33.838Z]   Module: messagebuilders
[2025-06-21T11:08:33.838Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T11:08:33.838Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.838Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.838Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T11:08:33.838Z]   Module: messagebuilders
[2025-06-21T11:08:33.838Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T11:08:33.838Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.838Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.838Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T11:08:33.838Z]   Module: messagebuilders
[2025-06-21T11:08:33.838Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T11:08:33.838Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.838Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.838Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T11:08:33.838Z]   Module: messagebuilders
[2025-06-21T11:08:33.838Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T11:08:33.838Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.838Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.838Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T11:08:33.838Z]   Module: messagebuilders
[2025-06-21T11:08:33.839Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T11:08:33.839Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.839Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.839Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T11:08:33.839Z]   Module: messagebuilders
[2025-06-21T11:08:33.839Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T11:08:33.839Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.839Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.839Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T11:08:33.839Z]   Module: messagebuilders
[2025-06-21T11:08:33.839Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T11:08:33.839Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.839Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.839Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T11:08:33.839Z]   Module: messagebuilders
[2025-06-21T11:08:33.839Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T11:08:33.839Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.839Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.839Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T11:08:33.839Z]   Module: messagebuilders
[2025-06-21T11:08:33.839Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T11:08:33.839Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.839Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.839Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T11:08:33.839Z]   Module: messagebuilders
[2025-06-21T11:08:33.840Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T11:08:33.840Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.840Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.840Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T11:08:33.840Z]   Module: messagebuilders
[2025-06-21T11:08:33.840Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T11:08:33.840Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.840Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.840Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T11:08:33.840Z]   Module: messagebuilders
[2025-06-21T11:08:33.840Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T11:08:33.840Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.840Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.840Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T11:08:33.840Z]   Module: messagebuilders
[2025-06-21T11:08:33.840Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T11:08:33.840Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.840Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.840Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T11:08:33.840Z]   Module: messagebuilders
[2025-06-21T11:08:33.840Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T11:08:33.840Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:33.840Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:33.840Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:08:33.840Z] Suite Duration: 1638ms
[2025-06-21T11:08:33.840Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T11:08:33.942Z]   [Arrange] - Precondition: Test environment prepared for "should export a service instance"
[2025-06-21T11:08:33.943Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:33.943Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:33.943Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:33.943Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T11:08:33.943Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:33.943Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:33.943Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:33.943Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:33.943Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T11:08:33.943Z]   [Act Log] - console: 2 outputs
[2025-06-21T11:08:33.943Z]   [Act Log] - process: 2 outputs
[2025-06-21T11:08:33.943Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:08:33.943Z]   [Act Log] - +94ms [console.log] Console log level: info
[2025-06-21T11:08:33.943Z]   [Act Log] - +94ms [process.stdout] Console log level: info

[2025-06-21T11:08:33.943Z]   [Act Log] - +102ms [console.log] File log level: warn
[2025-06-21T11:08:33.943Z]   [Act Log] - +102ms [process.stdout] File log level: warn

[2025-06-21T11:08:33.943Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:08:33.943Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:33.943Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:33.943Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:33.944Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:33.944Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:33.944Z]   [Assert Log] - Expected: Module should export a valid object or function
[2025-06-21T11:08:33.944Z]   [Assert Log] - Actual: typeof module !== "undefined" && module !== null
[2025-06-21T11:08:33.944Z]   [Assert Log] - Comparison: expect(module).toBeDefined() - PASSED
[2025-06-21T11:08:33.944Z]   [Assert Log] - Variable: module = [object Object] (WhatsAppService instance)
[2025-06-21T11:08:33.944Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:33.944Z]   [CASE END] - Duration: 287ms
[2025-06-21T11:08:33.944Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T11:08:33.944Z]   Module: whatsapp
[2025-06-21T11:08:33.945Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T11:08:34.022Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T11:08:34.022Z]   Module: whatsapp
[2025-06-21T11:08:34.022Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T11:08:34.023Z]   [Arrange] - Precondition: Test environment prepared for "should have required methods"
[2025-06-21T11:08:34.023Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:34.023Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:34.023Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:34.023Z]   [Act] - Step: Executing test logic for "should have required methods"
[2025-06-21T11:08:34.023Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:34.023Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:34.023Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:34.023Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:34.023Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:34.023Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:34.023Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:34.023Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:34.023Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:34.023Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:34.023Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:34.023Z]   [Assert Log] - Expected: Object should have property "required"
[2025-06-21T11:08:34.024Z]   [Assert Log] - Actual: object.hasOwnProperty("required") = true
[2025-06-21T11:08:34.024Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("required") - PASSED
[2025-06-21T11:08:34.024Z]   [Assert Log] - Variable: object.required = [Function] or [Object]
[2025-06-21T11:08:34.024Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:34.024Z]   [CASE END] - Duration: 79ms
[2025-06-21T11:08:34.119Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T11:08:34.119Z]   Module: whatsapp
[2025-06-21T11:08:34.119Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T11:08:34.119Z]   [Arrange] - Precondition: Test environment prepared for "should have configuration properties or be configurable"
[2025-06-21T11:08:34.119Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:34.120Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:34.120Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:34.120Z]   [Act] - Step: Executing test logic for "should have configuration properties or be configurable"
[2025-06-21T11:08:34.120Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:34.120Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:34.120Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:34.120Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:34.120Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:34.120Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:34.120Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:34.120Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:34.120Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:34.120Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:34.120Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:34.120Z]   [Assert Log] - Expected: Object should have property "configuration"
[2025-06-21T11:08:34.120Z]   [Assert Log] - Actual: object.hasOwnProperty("configuration") = true
[2025-06-21T11:08:34.120Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("configuration") - PASSED
[2025-06-21T11:08:34.120Z]   [Assert Log] - Variable: object.configuration = [Function] or [Object]
[2025-06-21T11:08:34.120Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:34.120Z]   [CASE END] - Duration: 95ms
[2025-06-21T11:08:34.206Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T11:08:34.207Z]   Module: whatsapp
[2025-06-21T11:08:34.207Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T11:08:34.283Z]   [Arrange] - Precondition: Test environment prepared for "should have retry configuration or error handling"
[2025-06-21T11:08:34.283Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:34.283Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:34.283Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:34.283Z]   [Act] - Step: Executing test logic for "should have retry configuration or error handling"
[2025-06-21T11:08:34.283Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:34.283Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:34.283Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:34.283Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:34.284Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:34.284Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:34.284Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:34.284Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:34.284Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:34.284Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:34.284Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:34.284Z]   [Assert Log] - Expected: Object should have property "retry"
[2025-06-21T11:08:34.284Z]   [Assert Log] - Actual: object.hasOwnProperty("retry") = true
[2025-06-21T11:08:34.284Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("retry") - PASSED
[2025-06-21T11:08:34.284Z]   [Assert Log] - Variable: object.retry = [Function] or [Object]
[2025-06-21T11:08:34.284Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:34.284Z]   [CASE END] - Duration: 77ms
[2025-06-21T11:08:34.285Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T11:08:34.285Z]   Module: whatsapp
[2025-06-21T11:08:34.285Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T11:08:34.422Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:08:34.422Z]   Module: order
[2025-06-21T11:08:34.422Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:08:34.422Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.422Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.422Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:08:34.422Z]   Module: order
[2025-06-21T11:08:34.422Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:08:34.422Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.422Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.422Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:08:34.422Z]   Module: order
[2025-06-21T11:08:34.422Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:08:34.422Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.422Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.422Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:08:34.422Z] Suite Duration: 2207ms
[2025-06-21T11:08:34.422Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:08:34.434Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T11:08:34.434Z]   Module: order
[2025-06-21T11:08:34.434Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T11:08:34.434Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.434Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.434Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T11:08:34.434Z]   Module: order
[2025-06-21T11:08:34.434Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T11:08:34.434Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.434Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.434Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T11:08:34.434Z]   Module: order
[2025-06-21T11:08:34.434Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T11:08:34.435Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.435Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.435Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T11:08:34.435Z]   Module: order
[2025-06-21T11:08:34.435Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T11:08:34.435Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.435Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.435Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:08:34.435Z] Suite Duration: 2205ms
[2025-06-21T11:08:34.435Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:08:34.457Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T11:08:34.457Z]   Module: order
[2025-06-21T11:08:34.457Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T11:08:34.457Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.457Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.457Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T11:08:34.457Z]   Module: order
[2025-06-21T11:08:34.457Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T11:08:34.457Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.457Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.457Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T11:08:34.457Z]   Module: order
[2025-06-21T11:08:34.457Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T11:08:34.457Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.457Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.457Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T11:08:34.458Z]   Module: order
[2025-06-21T11:08:34.458Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T11:08:34.458Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.458Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.458Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:08:34.458Z]   Module: order
[2025-06-21T11:08:34.458Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:08:34.458Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.458Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.458Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:08:34.458Z] Suite Duration: 2232ms
[2025-06-21T11:08:34.458Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:08:34.549Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T11:08:34.549Z]   Module: order
[2025-06-21T11:08:34.549Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T11:08:34.549Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.549Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.549Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T11:08:34.549Z]   Module: order
[2025-06-21T11:08:34.549Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T11:08:34.549Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.549Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.549Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T11:08:34.549Z]   Module: order
[2025-06-21T11:08:34.549Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T11:08:34.549Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.549Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.550Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T11:08:34.550Z]   Module: order
[2025-06-21T11:08:34.550Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T11:08:34.550Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.550Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.550Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T11:08:34.550Z]   Module: order
[2025-06-21T11:08:34.550Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T11:08:34.550Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.550Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.550Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:08:34.550Z] Suite Duration: 2345ms
[2025-06-21T11:08:34.550Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:08:34.573Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T11:08:34.573Z]   Module: session
[2025-06-21T11:08:34.573Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T11:08:34.573Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.573Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.573Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T11:08:34.573Z]   Module: session
[2025-06-21T11:08:34.573Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T11:08:34.573Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.573Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.573Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T11:08:34.573Z]   Module: session
[2025-06-21T11:08:34.573Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T11:08:34.573Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.573Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.573Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T11:08:34.573Z]   Module: session
[2025-06-21T11:08:34.573Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T11:08:34.573Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.573Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.573Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T11:08:34.573Z]   Module: session
[2025-06-21T11:08:34.573Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T11:08:34.573Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.573Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.573Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T11:08:34.573Z]   Module: session
[2025-06-21T11:08:34.573Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T11:08:34.573Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.574Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.574Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:08:34.574Z] Suite Duration: 2361ms
[2025-06-21T11:08:34.574Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:08:34.618Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call getAccessToken method"
[2025-06-21T11:08:34.619Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:34.619Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:34.619Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:34.619Z]   [Act] - Step: Executing test logic for "should be able to call getAccessToken method"
[2025-06-21T11:08:34.619Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:34.619Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:34.619Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:34.619Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:34.619Z]   [Act Log] - Captured 3 program outputs during execution
[2025-06-21T11:08:34.619Z]   [Act Log] - process: 3 outputs
[2025-06-21T11:08:34.619Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:08:34.619Z]   [Act Log] - +77ms [process.stdout] 2025-06-21 12:08:34:834 [37mdebug[39m: [37mAxios Request: POST https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default[39m
{
  "body": null
}

[2025-06-21T11:08:34.619Z]   [Act Log] - +331ms [process.stdout] 2025-06-21 12:08:34:834 [31merror[39m: [31mAxios Response Error: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "url": "https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
}

[2025-06-21T11:08:34.619Z]   [Act Log] - +332ms [process.stdout] 2025-06-21 12:08:34:834 [31merror[39m: [31mError getting access token: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "stack": "Error: getaddrinfo ENOTFOUND test-auth.example.com\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)",
  "request": {
    "url": "https://test-auth.example.com/token",
    "method": "POST",
    "params": "client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
  }
}

[2025-06-21T11:08:34.619Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:08:34.619Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:34.619Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:34.619Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:34.619Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:34.619Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:34.619Z]   [Assert Log] - Expected: Method "getAccessToken" should be callable without throwing
[2025-06-21T11:08:34.619Z]   [Assert Log] - Actual: service.getAccessToken() executed
[2025-06-21T11:08:34.619Z]   [Assert Log] - Comparison: expect(() => service.getAccessToken()).not.toThrow() - FAILED
[2025-06-21T11:08:34.619Z]   [Assert Log] - Error details: 2025-06-21 12:08:34:834 [31merror[39m: [31mAxios Response Error: getaddrinfo ENOTFOUND test-auth.example.com[39m
{
  "url": "https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default"
}

[2025-06-21T11:08:34.619Z]   [Assert Log] - Variable: error.code = "ENOTFOUND"
[2025-06-21T11:08:34.619Z]   [Assert Log] - Variable: error.hostname = "test-auth.example.com"
[2025-06-21T11:08:34.620Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:34.620Z]   [CASE END] - Duration: 336ms
[2025-06-21T11:08:34.620Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T11:08:34.620Z]   Module: whatsapp
[2025-06-21T11:08:34.620Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T11:08:34.659Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call sendBasicText method"
[2025-06-21T11:08:34.659Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:34.659Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:34.659Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:34.659Z]   [Act] - Step: Executing test logic for "should be able to call sendBasicText method"
[2025-06-21T11:08:34.659Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:34.659Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:34.659Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:34.659Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:34.659Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T11:08:34.659Z]   [Act Log] - process: 4 outputs
[2025-06-21T11:08:34.659Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:08:34.659Z]   [Act Log] - +32ms [process.stdout] 2025-06-21 12:08:34:834 [37mdebug[39m: [37mSending notification basic text message:[39m
{
  "recipientId": "+1234567890",
  "text": "test message",
  "messageType": "notification"
}

[2025-06-21T11:08:34.659Z]   [Act Log] - +33ms [process.stdout] 2025-06-21 12:08:34:834 [37mdebug[39m: [37mBuilding basic text message data:[39m
{
  "recipientId": "+1234567890",
  "text": "test message",
  "messageType": "notification",
  "template": "2222"
}

[2025-06-21T11:08:34.659Z]   [Act Log] - +34ms [process.stdout] 2025-06-21 12:08:34:834 [37mdebug[39m: [37mSending WhatsApp notification message:[39m
{
  "recipientId": "+1234567890",
  "type": "notification-messages",
  "templateId": "2222"
}

[2025-06-21T11:08:34.659Z]   [Act Log] - +35ms [process.stdout] 2025-06-21 12:08:34:834 [37mdebug[39m: [37mAxios Request: POST https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default[39m
{
  "body": null
}

[2025-06-21T11:08:34.659Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:08:34.659Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:34.660Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:34.660Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:34.660Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:34.660Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:34.660Z]   [Assert Log] - Expected: Method "sendBasicText" should be callable without throwing
[2025-06-21T11:08:34.660Z]   [Assert Log] - Actual: service.sendBasicText() executed
[2025-06-21T11:08:34.660Z]   [Assert Log] - Comparison: expect(() => service.sendBasicText()).not.toThrow() - PASSED
[2025-06-21T11:08:34.660Z]   [Assert Log] - Variable: result = [Promise] (async operation completed)
[2025-06-21T11:08:34.660Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:34.660Z]   [CASE END] - Duration: 40ms
[2025-06-21T11:08:34.660Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T11:08:34.660Z]   Module: whatsapp
[2025-06-21T11:08:34.660Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T11:08:34.710Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call sendQuickReply method"
[2025-06-21T11:08:34.711Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:34.711Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:34.711Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:34.711Z]   [Act] - Step: Executing test logic for "should be able to call sendQuickReply method"
[2025-06-21T11:08:34.711Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:08:34.711Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:34.711Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:34.711Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:34.711Z]   [Act Log] - Captured 1 program outputs during execution
[2025-06-21T11:08:34.711Z]   [Act Log] - process: 1 outputs
[2025-06-21T11:08:34.711Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:08:34.711Z]   [Act Log] - +49ms [process.stdout] 2025-06-21 12:08:34:834 [37mdebug[39m: [37mSending notification quick reply message:[39m
{
  "recipientId": "+1234567890",
  "options": "{\"text\":\"test\",\"buttons\":[]}",
  "messageType": "notification"
}

[2025-06-21T11:08:34.711Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:08:34.711Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:34.711Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:34.711Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:34.711Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:34.711Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:34.711Z]   [Assert Log] - Expected: Method "sendQuickReply" should be callable without throwing
[2025-06-21T11:08:34.711Z]   [Assert Log] - Actual: service.sendQuickReply() executed
[2025-06-21T11:08:34.711Z]   [Assert Log] - Comparison: expect(() => service.sendQuickReply()).not.toThrow() - PASSED
[2025-06-21T11:08:34.712Z]   [Assert Log] - Variable: result = [Promise] (async operation completed)
[2025-06-21T11:08:34.712Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:34.712Z]   [CASE END] - Duration: 51ms
[2025-06-21T11:08:34.715Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:08:34.715Z] Suite Duration: 2463ms
[2025-06-21T11:08:34.715Z] Suite Results: 8 passed, 0 failed, 0 skipped
[2025-06-21T11:08:34.963Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T11:08:34.964Z]   Module: dialog
[2025-06-21T11:08:34.964Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T11:08:34.964Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.964Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.964Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T11:08:34.964Z]   Module: dialog
[2025-06-21T11:08:34.964Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T11:08:34.964Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.964Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.964Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T11:08:34.964Z]   Module: dialog
[2025-06-21T11:08:34.964Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T11:08:34.964Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.964Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.964Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T11:08:34.964Z]   Module: dialog
[2025-06-21T11:08:34.964Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T11:08:34.964Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.964Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.964Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T11:08:34.964Z]   Module: dialog
[2025-06-21T11:08:34.964Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T11:08:34.964Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.964Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.964Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T11:08:34.964Z]   Module: dialog
[2025-06-21T11:08:34.964Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T11:08:34.964Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:34.964Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:34.964Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:08:34.964Z] Suite Duration: 2787ms
[2025-06-21T11:08:34.964Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:08:35.030Z] 
================================================================================
[2025-06-21T11:08:35.030Z] Test Run Finished
[2025-06-21T11:08:35.030Z] End Timestamp: 2025-06-21T11:08:35.030Z
[2025-06-21T11:08:35.030Z] Total Duration: 3114ms (3.11s)
[2025-06-21T11:08:35.030Z] 
[STATISTICS]
[2025-06-21T11:08:35.030Z] Total Test Suites: 12
[2025-06-21T11:08:35.030Z] Passed Test Suites: 1
[2025-06-21T11:08:35.030Z] Failed Test Suites: 0
[2025-06-21T11:08:35.030Z] Total Tests: 94
[2025-06-21T11:08:35.030Z] Passed Tests: 8
[2025-06-21T11:08:35.030Z] Failed Tests: 0
[2025-06-21T11:08:35.030Z] Skipped Tests: 86
[2025-06-21T11:08:35.030Z] Success Rate: 8.51%
[2025-06-21T11:08:35.030Z] Overall Result: FAILURE
[2025-06-21T11:08:35.030Z] ================================================================================
