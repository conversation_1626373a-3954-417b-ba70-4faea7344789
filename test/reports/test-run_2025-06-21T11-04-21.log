[2025-06-21T11:04:21.742Z] ================================================================================
[2025-06-21T11:04:21.742Z] Test Run Started
[2025-06-21T11:04:21.742Z] Timestamp: 2025-06-21T11:04:21.741Z
[2025-06-21T11:04:21.742Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:04:21.742Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:04:21.742Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:04:21.742Z] ================================================================================
[2025-06-21T11:04:21.746Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:04:21.746Z] Total Test Suites: 12
[2025-06-21T11:04:21.746Z] Test Environment: test
[2025-06-21T11:04:21.851Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:04:21.851Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:04:21.852Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:04:21.852Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:04:21.852Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:04:21.852Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.852Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:04:21.852Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.853Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:04:21.853Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.853Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:04:21.853Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.853Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:04:21.853Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.853Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:04:21.853Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.853Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:04:21.853Z] Suite Display Name: UNIT
[2025-06-21T11:04:21.853Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:04:21.853Z] Suite Display Name: UNIT
[2025-06-21T11:04:23.869Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T11:04:23.870Z]   Module: restaurantstore
[2025-06-21T11:04:23.870Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T11:04:23.871Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.871Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.871Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T11:04:23.872Z]   Module: restaurantstore
[2025-06-21T11:04:23.872Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T11:04:23.872Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.872Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.873Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T11:04:23.873Z]   Module: restaurantstore
[2025-06-21T11:04:23.873Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T11:04:23.873Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.874Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.874Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T11:04:23.874Z]   Module: restaurantstore
[2025-06-21T11:04:23.875Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T11:04:23.875Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.875Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.875Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T11:04:23.875Z]   Module: restaurantstore
[2025-06-21T11:04:23.875Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T11:04:23.875Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.875Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.875Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T11:04:23.875Z]   Module: restaurantstore
[2025-06-21T11:04:23.875Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T11:04:23.876Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.876Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.876Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T11:04:23.876Z]   Module: restaurantstore
[2025-06-21T11:04:23.876Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T11:04:23.876Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.876Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.876Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T11:04:23.876Z]   Module: restaurantstore
[2025-06-21T11:04:23.876Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T11:04:23.877Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.877Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.877Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T11:04:23.877Z]   Module: restaurantstore
[2025-06-21T11:04:23.877Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T11:04:23.877Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.877Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.877Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:04:23.877Z] Suite Duration: 1738ms
[2025-06-21T11:04:23.877Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:04:23.918Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T11:04:23.918Z]   Module: linkgenerator
[2025-06-21T11:04:23.918Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T11:04:23.919Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.919Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.919Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T11:04:23.919Z]   Module: linkgenerator
[2025-06-21T11:04:23.919Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T11:04:23.919Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.919Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.919Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T11:04:23.919Z]   Module: linkgenerator
[2025-06-21T11:04:23.919Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T11:04:23.919Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.919Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.919Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T11:04:23.919Z]   Module: linkgenerator
[2025-06-21T11:04:23.919Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T11:04:23.919Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.919Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.919Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T11:04:23.919Z]   Module: linkgenerator
[2025-06-21T11:04:23.920Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T11:04:23.920Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.920Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.920Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T11:04:23.920Z]   Module: linkgenerator
[2025-06-21T11:04:23.920Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T11:04:23.920Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.920Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.920Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:04:23.920Z] Suite Duration: 1769ms
[2025-06-21T11:04:23.920Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:04:23.983Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T11:04:23.983Z]   Module: sessionservice
[2025-06-21T11:04:23.983Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T11:04:23.984Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.984Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.984Z] 
  [CASE START] - ***********: should have required methods
[2025-06-21T11:04:23.984Z]   Module: sessionservice
[2025-06-21T11:04:23.984Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T11:04:23.984Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.984Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.984Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T11:04:23.984Z]   Module: sessionservice
[2025-06-21T11:04:23.984Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T11:04:23.984Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.984Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.984Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T11:04:23.984Z]   Module: sessionservice
[2025-06-21T11:04:23.984Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T11:04:23.984Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.984Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.984Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T11:04:23.984Z]   Module: sessionservice
[2025-06-21T11:04:23.984Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T11:04:23.984Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.984Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.984Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T11:04:23.984Z]   Module: sessionservice
[2025-06-21T11:04:23.984Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T11:04:23.985Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.985Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.985Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T11:04:23.985Z]   Module: sessionservice
[2025-06-21T11:04:23.985Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T11:04:23.985Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.985Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.985Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T11:04:23.985Z]   Module: sessionservice
[2025-06-21T11:04:23.985Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T11:04:23.985Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.985Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.985Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T11:04:23.985Z]   Module: sessionservice
[2025-06-21T11:04:23.985Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T11:04:23.985Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.985Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.985Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T11:04:23.985Z]   Module: sessionservice
[2025-06-21T11:04:23.986Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T11:04:23.986Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.986Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.987Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T11:04:23.987Z]   Module: sessionservice
[2025-06-21T11:04:23.987Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T11:04:23.987Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:23.987Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:23.987Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:04:23.987Z] Suite Duration: 1890ms
[2025-06-21T11:04:23.987Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T11:04:24.139Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T11:04:24.139Z]   Module: whatsapp
[2025-06-21T11:04:24.139Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T11:04:24.145Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T11:04:24.145Z]   Module: messagebuilders
[2025-06-21T11:04:24.145Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T11:04:24.145Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.146Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.146Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T11:04:24.146Z]   Module: messagebuilders
[2025-06-21T11:04:24.146Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T11:04:24.146Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.146Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.146Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T11:04:24.146Z]   Module: messagebuilders
[2025-06-21T11:04:24.146Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T11:04:24.147Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.147Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.147Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T11:04:24.147Z]   Module: messagebuilders
[2025-06-21T11:04:24.147Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T11:04:24.147Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.147Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.147Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T11:04:24.147Z]   Module: messagebuilders
[2025-06-21T11:04:24.147Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T11:04:24.147Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.147Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.147Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T11:04:24.147Z]   Module: messagebuilders
[2025-06-21T11:04:24.147Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T11:04:24.147Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.147Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.147Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T11:04:24.148Z]   Module: messagebuilders
[2025-06-21T11:04:24.148Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T11:04:24.148Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.148Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.148Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T11:04:24.148Z]   Module: messagebuilders
[2025-06-21T11:04:24.148Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T11:04:24.148Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.148Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.148Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T11:04:24.148Z]   Module: messagebuilders
[2025-06-21T11:04:24.148Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T11:04:24.148Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.148Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.148Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T11:04:24.148Z]   Module: messagebuilders
[2025-06-21T11:04:24.149Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T11:04:24.149Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.150Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.150Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T11:04:24.150Z]   Module: messagebuilders
[2025-06-21T11:04:24.150Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T11:04:24.151Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.151Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.151Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T11:04:24.151Z]   Module: messagebuilders
[2025-06-21T11:04:24.151Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T11:04:24.151Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.151Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.151Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T11:04:24.151Z]   Module: messagebuilders
[2025-06-21T11:04:24.151Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T11:04:24.152Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.152Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.152Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T11:04:24.152Z]   Module: messagebuilders
[2025-06-21T11:04:24.152Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T11:04:24.152Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.152Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.152Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T11:04:24.152Z]   Module: messagebuilders
[2025-06-21T11:04:24.152Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T11:04:24.152Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.152Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.152Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T11:04:24.152Z]   Module: messagebuilders
[2025-06-21T11:04:24.152Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T11:04:24.152Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.153Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.153Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:04:24.153Z] Suite Duration: 1986ms
[2025-06-21T11:04:24.153Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T11:04:24.302Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T11:04:24.302Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.302Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T11:04:24.302Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.302Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.302Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T11:04:24.302Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.302Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T11:04:24.302Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.302Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.302Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T11:04:24.303Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.303Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T11:04:24.303Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.303Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.303Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T11:04:24.303Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.303Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T11:04:24.303Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.303Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.303Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T11:04:24.303Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.303Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T11:04:24.303Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.303Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.303Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T11:04:24.303Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.303Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T11:04:24.303Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.303Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.303Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T11:04:24.303Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.303Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T11:04:24.303Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.303Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.304Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T11:04:24.304Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.304Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T11:04:24.304Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.304Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.304Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T11:04:24.304Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.304Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T11:04:24.304Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.304Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.304Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T11:04:24.304Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.304Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T11:04:24.304Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.304Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.304Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T11:04:24.304Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.304Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T11:04:24.304Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.304Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.304Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T11:04:24.304Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.304Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T11:04:24.305Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.305Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.305Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T11:04:24.305Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.305Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T11:04:24.305Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.305Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.305Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T11:04:24.305Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.305Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T11:04:24.305Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.305Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.305Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T11:04:24.305Z]   Module: sessionidgenerator
[2025-06-21T11:04:24.305Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T11:04:24.305Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.305Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.305Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:04:24.305Z] Suite Duration: 2111ms
[2025-06-21T11:04:24.305Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T11:04:24.352Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T11:04:24.352Z]   Module: whatsapp
[2025-06-21T11:04:24.353Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T11:04:24.354Z]   [Arrange] - Precondition: Test environment prepared for "should export a service instance"
[2025-06-21T11:04:24.354Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:24.354Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:24.354Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:24.354Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T11:04:24.354Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:04:24.354Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:24.354Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:24.354Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:24.354Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:24.354Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:24.354Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:24.355Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:24.355Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:24.355Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:24.355Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:24.355Z]   [Assert Log] - Expected result: Object/Function export (truthy value)
[2025-06-21T11:04:24.355Z]   [Assert Log] - Actual result: Module exported successfully (object/function)
[2025-06-21T11:04:24.355Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:24.355Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:24.355Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:24.355Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:24.355Z]   [CASE END] - Duration: 361ms
[2025-06-21T11:04:24.456Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T11:04:24.456Z]   Module: whatsapp
[2025-06-21T11:04:24.456Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T11:04:24.456Z]   [Arrange] - Precondition: Test environment prepared for "should have required methods"
[2025-06-21T11:04:24.457Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:24.457Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:24.457Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:24.457Z]   [Act] - Step: Executing test logic for "should have required methods"
[2025-06-21T11:04:24.457Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:04:24.457Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:24.457Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:24.457Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:24.457Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:24.457Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:24.457Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:24.457Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:24.457Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:24.457Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:24.457Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:24.457Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:24.457Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:24.457Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:24.457Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:24.457Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:24.457Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:24.457Z]   [CASE END] - Duration: 104ms
[2025-06-21T11:04:24.559Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T11:04:24.560Z]   Module: whatsapp
[2025-06-21T11:04:24.560Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T11:04:24.561Z]   [Arrange] - Precondition: Test environment prepared for "should have configuration properties or be configurable"
[2025-06-21T11:04:24.561Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:24.561Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:24.561Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:24.561Z]   [Act] - Step: Executing test logic for "should have configuration properties or be configurable"
[2025-06-21T11:04:24.561Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:04:24.561Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:24.561Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:24.561Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:24.561Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:24.561Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:24.561Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:24.561Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:24.561Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:24.561Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:24.561Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:24.562Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:24.562Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:24.562Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:24.562Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:24.562Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:24.562Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:24.562Z]   [CASE END] - Duration: 104ms
[2025-06-21T11:04:24.644Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T11:04:24.644Z]   Module: whatsapp
[2025-06-21T11:04:24.644Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T11:04:24.756Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T11:04:24.756Z]   Module: whatsapp
[2025-06-21T11:04:24.756Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T11:04:24.756Z]   [Arrange] - Precondition: Test environment prepared for "should have retry configuration or error handling"
[2025-06-21T11:04:24.756Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:24.756Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:24.756Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:24.756Z]   [Act] - Step: Executing test logic for "should have retry configuration or error handling"
[2025-06-21T11:04:24.757Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:04:24.757Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:24.757Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:24.757Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:24.757Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:24.757Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:24.757Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:24.757Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:24.757Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:24.757Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:24.757Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:24.757Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:24.757Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:24.757Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:24.757Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:24.757Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:24.757Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:24.757Z]   [CASE END] - Duration: 108ms
[2025-06-21T11:04:24.847Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:04:24.847Z]   Module: order
[2025-06-21T11:04:24.847Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:04:24.847Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.847Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.848Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:04:24.848Z]   Module: order
[2025-06-21T11:04:24.848Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:04:24.848Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.848Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.848Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:04:24.848Z]   Module: order
[2025-06-21T11:04:24.848Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:04:24.848Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.848Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.848Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:04:24.848Z] Suite Duration: 2727ms
[2025-06-21T11:04:24.848Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:04:24.929Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T11:04:24.929Z]   Module: order
[2025-06-21T11:04:24.929Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T11:04:24.929Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.930Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T11:04:24.930Z]   Module: order
[2025-06-21T11:04:24.930Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T11:04:24.930Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.930Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T11:04:24.930Z]   Module: order
[2025-06-21T11:04:24.930Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T11:04:24.930Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.930Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T11:04:24.930Z]   Module: order
[2025-06-21T11:04:24.930Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T11:04:24.930Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.930Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.930Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:04:24.930Z]   Module: order
[2025-06-21T11:04:24.931Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:04:24.931Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:24.931Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:24.931Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:04:24.931Z] Suite Duration: 2752ms
[2025-06-21T11:04:24.931Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:04:25.024Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T11:04:25.024Z]   Module: order
[2025-06-21T11:04:25.024Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T11:04:25.024Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.024Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.024Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T11:04:25.024Z]   Module: order
[2025-06-21T11:04:25.024Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T11:04:25.025Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.025Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.025Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T11:04:25.025Z]   Module: order
[2025-06-21T11:04:25.025Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T11:04:25.025Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.025Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.025Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T11:04:25.025Z]   Module: order
[2025-06-21T11:04:25.025Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T11:04:25.025Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.025Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.025Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:04:25.025Z] Suite Duration: 2913ms
[2025-06-21T11:04:25.025Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:04:25.053Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T11:04:25.054Z]   Module: whatsapp
[2025-06-21T11:04:25.054Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T11:04:25.090Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T11:04:25.090Z]   Module: order
[2025-06-21T11:04:25.090Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T11:04:25.090Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.090Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.090Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T11:04:25.090Z]   Module: order
[2025-06-21T11:04:25.090Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T11:04:25.090Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.090Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.090Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T11:04:25.090Z]   Module: order
[2025-06-21T11:04:25.090Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T11:04:25.090Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.090Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.090Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T11:04:25.090Z]   Module: order
[2025-06-21T11:04:25.090Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T11:04:25.090Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.091Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.091Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T11:04:25.091Z]   Module: order
[2025-06-21T11:04:25.091Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T11:04:25.091Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.091Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.091Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:04:25.091Z] Suite Duration: 2965ms
[2025-06-21T11:04:25.091Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:04:25.122Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call sendBasicText method"
[2025-06-21T11:04:25.122Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:25.122Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:25.122Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:25.122Z]   [Act] - Step: Executing test logic for "should be able to call sendBasicText method"
[2025-06-21T11:04:25.123Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:04:25.123Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:25.123Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:25.123Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:25.123Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T11:04:25.123Z]   [Act Log] - process: 4 outputs
[2025-06-21T11:04:25.123Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:04:25.123Z]   [Act Log] - +57ms [process.stdout] 2025-06-21 12:04:25:425 [37mdebug[39m: [37mSending notification basic text message:[39m
{
  "recipientId": "+1234567890",
  "text": "test message",
  "messageType": "notification"
}

[2025-06-21T11:04:25.123Z]   [Act Log] - +58ms [process.stdout] 2025-06-21 12:04:25:425 [37mdebug[39m: [37mBuilding basic text message data:[39m
{
  "recipientId": "+1234567890",
  "text": "test message",
  "messageType": "notification",
  "template": "2222"
}

[2025-06-21T11:04:25.123Z]   [Act Log] - +62ms [process.stdout] 2025-06-21 12:04:25:425 [37mdebug[39m: [37mSending WhatsApp notification message:[39m
{
  "recipientId": "+1234567890",
  "type": "notification-messages",
  "templateId": "2222"
}

[2025-06-21T11:04:25.123Z]   [Act Log] - +64ms [process.stdout] 2025-06-21 12:04:25:425 [37mdebug[39m: [37mAxios Request: POST https://test-auth.example.com/token?client_id=test-client-id&client_secret=test-client-secret&p=b2c_1_login&grant_type=client_credentials&Scope=https%3A%2F%2Flogin.payemoji.com%2Fauth%2F.default[39m
{
  "body": null
}

[2025-06-21T11:04:25.123Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:04:25.123Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:25.123Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:25.123Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:25.123Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:25.123Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:25.124Z]   [Assert Log] - Expected result: Function callable (no exceptions)
[2025-06-21T11:04:25.124Z]   [Assert Log] - Actual result: Function executed without exceptions
[2025-06-21T11:04:25.124Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:25.124Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:25.124Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:25.124Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:25.124Z]   [CASE END] - Duration: 68ms
[2025-06-21T11:04:25.125Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T11:04:25.125Z]   Module: whatsapp
[2025-06-21T11:04:25.125Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T11:04:25.194Z]   [Arrange] - Precondition: Test environment prepared for "should be able to call sendQuickReply method"
[2025-06-21T11:04:25.194Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:25.194Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:25.194Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:25.194Z]   [Act] - Step: Executing test logic for "should be able to call sendQuickReply method"
[2025-06-21T11:04:25.194Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:04:25.194Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:25.194Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:25.194Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:25.194Z]   [Act Log] - Captured 1 program outputs during execution
[2025-06-21T11:04:25.194Z]   [Act Log] - process: 1 outputs
[2025-06-21T11:04:25.194Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:04:25.194Z]   [Act Log] - +67ms [process.stdout] 2025-06-21 12:04:25:425 [37mdebug[39m: [37mSending notification quick reply message:[39m
{
  "recipientId": "+1234567890",
  "options": "{\"text\":\"test\",\"buttons\":[]}",
  "messageType": "notification"
}

[2025-06-21T11:04:25.194Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:04:25.194Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:25.195Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:25.195Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:25.195Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:25.195Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:25.195Z]   [Assert Log] - Expected result: Function callable (no exceptions)
[2025-06-21T11:04:25.195Z]   [Assert Log] - Actual result: Function executed without exceptions
[2025-06-21T11:04:25.195Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:25.195Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:25.195Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:25.195Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:25.195Z]   [CASE END] - Duration: 70ms
[2025-06-21T11:04:25.199Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:04:25.199Z] Suite Duration: 3016ms
[2025-06-21T11:04:25.199Z] Suite Results: 8 passed, 0 failed, 0 skipped
[2025-06-21T11:04:25.211Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T11:04:25.212Z]   Module: session
[2025-06-21T11:04:25.212Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T11:04:25.212Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.212Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.212Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T11:04:25.212Z]   Module: session
[2025-06-21T11:04:25.212Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T11:04:25.212Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.212Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.212Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T11:04:25.212Z]   Module: session
[2025-06-21T11:04:25.212Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T11:04:25.212Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.212Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.212Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T11:04:25.212Z]   Module: session
[2025-06-21T11:04:25.213Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T11:04:25.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.213Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T11:04:25.213Z]   Module: session
[2025-06-21T11:04:25.213Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T11:04:25.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.213Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T11:04:25.213Z]   Module: session
[2025-06-21T11:04:25.213Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T11:04:25.213Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.213Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.213Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:04:25.213Z] Suite Duration: 3089ms
[2025-06-21T11:04:25.213Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:04:25.642Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T11:04:25.642Z]   Module: dialog
[2025-06-21T11:04:25.642Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T11:04:25.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.642Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T11:04:25.642Z]   Module: dialog
[2025-06-21T11:04:25.642Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T11:04:25.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.642Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T11:04:25.642Z]   Module: dialog
[2025-06-21T11:04:25.642Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T11:04:25.642Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.642Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.643Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T11:04:25.643Z]   Module: dialog
[2025-06-21T11:04:25.643Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T11:04:25.643Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.643Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.643Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T11:04:25.643Z]   Module: dialog
[2025-06-21T11:04:25.643Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T11:04:25.643Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.643Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.643Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T11:04:25.643Z]   Module: dialog
[2025-06-21T11:04:25.643Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T11:04:25.643Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:25.643Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:25.643Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:04:25.643Z] Suite Duration: 3478ms
[2025-06-21T11:04:25.643Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:04:25.734Z] 
================================================================================
[2025-06-21T11:04:25.734Z] Test Run Finished
[2025-06-21T11:04:25.734Z] End Timestamp: 2025-06-21T11:04:25.734Z
[2025-06-21T11:04:25.734Z] Total Duration: 3993ms (3.99s)
[2025-06-21T11:04:25.734Z] 
[STATISTICS]
[2025-06-21T11:04:25.734Z] Total Test Suites: 12
[2025-06-21T11:04:25.735Z] Passed Test Suites: 1
[2025-06-21T11:04:25.735Z] Failed Test Suites: 0
[2025-06-21T11:04:25.735Z] Total Tests: 94
[2025-06-21T11:04:25.735Z] Passed Tests: 8
[2025-06-21T11:04:25.735Z] Failed Tests: 0
[2025-06-21T11:04:25.735Z] Skipped Tests: 86
[2025-06-21T11:04:25.735Z] Success Rate: 8.51%
[2025-06-21T11:04:25.735Z] Overall Result: FAILURE
[2025-06-21T11:04:25.735Z] ================================================================================
