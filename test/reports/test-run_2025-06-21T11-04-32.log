[2025-06-21T11:04:32.423Z] ================================================================================
[2025-06-21T11:04:32.423Z] Test Run Started
[2025-06-21T11:04:32.423Z] Timestamp: 2025-06-21T11:04:32.423Z
[2025-06-21T11:04:32.423Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:04:32.423Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:04:32.423Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:04:32.423Z] ================================================================================
[2025-06-21T11:04:32.427Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:04:32.427Z] Total Test Suites: 14
[2025-06-21T11:04:32.427Z] Test Environment: test
[2025-06-21T11:04:32.435Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T11:04:32.435Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:35.355Z] 
  [CASE START] - TC-674469B3: should process incoming message and create session
[2025-06-21T11:04:35.355Z]   Module: whatsapp
[2025-06-21T11:04:35.355Z]   Full Path: WhatsApp Webhook Integration › should process incoming message and create session
[2025-06-21T11:04:35.355Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:35.355Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:35.355Z] 
  [CASE START] - TC-432D970F: should reject webhook with invalid signature
[2025-06-21T11:04:35.355Z]   Module: whatsapp
[2025-06-21T11:04:35.355Z]   Full Path: WhatsApp Webhook Integration › should reject webhook with invalid signature
[2025-06-21T11:04:35.355Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:35.355Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:35.355Z] 
  [CASE START] - TC-3376EAD8: should handle malformed webhook payload
[2025-06-21T11:04:35.355Z]   Module: whatsapp
[2025-06-21T11:04:35.356Z]   Full Path: WhatsApp Webhook Integration › should handle malformed webhook payload
[2025-06-21T11:04:35.356Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:35.356Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:35.356Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T11:04:35.356Z] Suite Duration: 2901ms
[2025-06-21T11:04:35.356Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:04:35.356Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T11:04:35.356Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:35.505Z] 
  [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-21T11:04:35.506Z]   Module: refund
[2025-06-21T11:04:35.506Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-21T11:04:35.506Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:35.506Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:35.506Z] 
  [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-21T11:04:35.506Z]   Module: refund
[2025-06-21T11:04:35.506Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-21T11:04:35.506Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:35.506Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:35.506Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T11:04:35.506Z] Suite Duration: 144ms
[2025-06-21T11:04:35.506Z] Suite Results: 0 passed, 0 failed, 2 skipped
[2025-06-21T11:04:35.506Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T11:04:35.506Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:36.806Z] 
  [CASE START] - TC-2E236918: should create real payment intent with Stripe
[2025-06-21T11:04:36.806Z]   Module: real
[2025-06-21T11:04:36.806Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should create real payment intent with Stripe
[2025-06-21T11:04:36.806Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.806Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.806Z] 
  [CASE START] - TC-18AE2E8F: should retrieve real payment intent status
[2025-06-21T11:04:36.806Z]   Module: real
[2025-06-21T11:04:36.806Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should retrieve real payment intent status
[2025-06-21T11:04:36.806Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.806Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.806Z] 
  [CASE START] - TC-6AADE1B7: should handle payment with test card
[2025-06-21T11:04:36.806Z]   Module: real
[2025-06-21T11:04:36.806Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle payment with test card
[2025-06-21T11:04:36.806Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.806Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.806Z] 
  [CASE START] - TC-54B75689: should handle declined card
[2025-06-21T11:04:36.806Z]   Module: real
[2025-06-21T11:04:36.806Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle declined card
[2025-06-21T11:04:36.806Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.806Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.806Z] 
  [CASE START] - TC-332A1C6D: should create real Stripe customer
[2025-06-21T11:04:36.806Z]   Module: real
[2025-06-21T11:04:36.806Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should create real Stripe customer
[2025-06-21T11:04:36.806Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.806Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.806Z] 
  [CASE START] - TC-164683AD: should retrieve real Stripe customer
[2025-06-21T11:04:36.807Z]   Module: real
[2025-06-21T11:04:36.807Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should retrieve real Stripe customer
[2025-06-21T11:04:36.807Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.807Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.807Z] 
  [CASE START] - TC-5FD44F7E: should process real webhook signature validation
[2025-06-21T11:04:36.807Z]   Module: real
[2025-06-21T11:04:36.807Z]   Full Path: Real Stripe Payment Integration Tests › Real Webhook Processing › should process real webhook signature validation
[2025-06-21T11:04:36.807Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.807Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.807Z] 
  [CASE START] - TC-74FBC87B: should handle invalid amount
[2025-06-21T11:04:36.807Z]   Module: real
[2025-06-21T11:04:36.807Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid amount
[2025-06-21T11:04:36.807Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.807Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.807Z] 
  [CASE START] - TC-56B8BFFE: should handle invalid currency
[2025-06-21T11:04:36.807Z]   Module: real
[2025-06-21T11:04:36.807Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid currency
[2025-06-21T11:04:36.807Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:36.807Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:36.807Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T11:04:36.807Z] Suite Duration: 1291ms
[2025-06-21T11:04:36.807Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:04:36.807Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T11:04:36.807Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:37.626Z] 
  [CASE START] - TC-3CAE5E97: should create a new order
[2025-06-21T11:04:37.626Z]   Module: graphql
[2025-06-21T11:04:37.626Z]   Full Path: Order GraphQL Mutations › should create a new order
[2025-06-21T11:04:37.627Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:37.627Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:37.627Z] 
  [CASE START] - TC-6385F57E: should fail to create order with invalid restaurant ID
[2025-06-21T11:04:37.627Z]   Module: graphql
[2025-06-21T11:04:37.627Z]   Full Path: Order GraphQL Mutations › should fail to create order with invalid restaurant ID
[2025-06-21T11:04:37.627Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:37.627Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:37.627Z] 
  [CASE START] - TC-7D51EFC9: should fail to create order without authentication
[2025-06-21T11:04:37.627Z]   Module: graphql
[2025-06-21T11:04:37.627Z]   Full Path: Order GraphQL Mutations › should fail to create order without authentication
[2025-06-21T11:04:37.627Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:37.627Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:37.627Z] 
  [CASE START] - TC-7912DF71: should update order status
[2025-06-21T11:04:37.627Z]   Module: graphql
[2025-06-21T11:04:37.627Z]   Full Path: Order GraphQL Mutations › should update order status
[2025-06-21T11:04:37.627Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:37.627Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:37.627Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T11:04:37.627Z] Suite Duration: 816ms
[2025-06-21T11:04:37.627Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:04:37.627Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T11:04:37.627Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:39.120Z] 
  [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-21T11:04:39.120Z]   Module: graphql
[2025-06-21T11:04:39.120Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-21T11:04:39.448Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type in schema"
[2025-06-21T11:04:39.448Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:39.448Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:39.448Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:39.448Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-21T11:04:39.448Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T11:04:39.448Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:39.448Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:39.448Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:39.448Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T11:04:39.448Z]   [Act Log] - console: 2 outputs
[2025-06-21T11:04:39.448Z]   [Act Log] - process: 2 outputs
[2025-06-21T11:04:39.448Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:04:39.448Z]   [Act Log] - +26ms [console.log] Console log level: debug
[2025-06-21T11:04:39.448Z]   [Act Log] - +26ms [process.stdout] Console log level: debug

[2025-06-21T11:04:39.448Z]   [Act Log] - +28ms [console.log] File log level: debug
[2025-06-21T11:04:39.448Z]   [Act Log] - +28ms [process.stdout] File log level: debug

[2025-06-21T11:04:39.448Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:04:39.448Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:39.448Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:39.448Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:39.448Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:39.448Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:39.448Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:39.448Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:39.448Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:39.448Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:39.448Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:39.448Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:39.448Z]   [CASE END] - Duration: 356ms
[2025-06-21T11:04:39.449Z] 
  [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-21T11:04:39.449Z]   Module: graphql
[2025-06-21T11:04:39.449Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-21T11:04:39.481Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderStatus enum in schema"
[2025-06-21T11:04:39.481Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:39.481Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:39.481Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:39.481Z]   [Act] - Step: Executing test logic for "should have OrderStatus enum in schema"
[2025-06-21T11:04:39.481Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T11:04:39.481Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:39.481Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:39.481Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:39.481Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:39.481Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:39.481Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:39.481Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:39.482Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:39.482Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:39.482Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:39.482Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:39.482Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:39.482Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:39.482Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:39.482Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:39.482Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:39.482Z]   [CASE END] - Duration: 32ms
[2025-06-21T11:04:39.482Z] 
  [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-21T11:04:39.482Z]   Module: graphql
[2025-06-21T11:04:39.482Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-21T11:04:39.517Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderInput type in schema"
[2025-06-21T11:04:39.517Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:39.517Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:39.517Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:39.517Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-21T11:04:39.517Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T11:04:39.517Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:39.517Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:39.517Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:39.517Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:39.517Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:39.517Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:39.517Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:39.517Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:39.517Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:39.517Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:39.517Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:39.517Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:39.517Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:39.517Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:39.518Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:39.518Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:39.518Z]   [CASE END] - Duration: 35ms
[2025-06-21T11:04:39.520Z] 
  [CASE START] - TC-1EE21EDD: should validate GraphQL order operations
[2025-06-21T11:04:39.520Z]   Module: graphql
[2025-06-21T11:04:39.520Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should validate GraphQL order operations
[2025-06-21T11:04:39.520Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:39.520Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:39.520Z] 
  [CASE START] - TC-8227B16: should handle malformed order queries
[2025-06-21T11:04:39.520Z]   Module: graphql
[2025-06-21T11:04:39.520Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should handle malformed order queries
[2025-06-21T11:04:39.520Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:39.520Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:39.520Z] 
  [CASE START] - TC-C2E23B4: should validate required arguments
[2025-06-21T11:04:39.520Z]   Module: graphql
[2025-06-21T11:04:39.520Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should validate required arguments
[2025-06-21T11:04:39.521Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:39.521Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:39.521Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T11:04:39.521Z] Suite Duration: 1888ms
[2025-06-21T11:04:39.521Z] Suite Results: 3 passed, 0 failed, 3 skipped
[2025-06-21T11:04:39.521Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T11:04:39.521Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:40.993Z] 
  [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-21T11:04:40.993Z]   Module: graphql
[2025-06-21T11:04:40.993Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-21T11:04:41.052Z]   [Arrange] - Precondition: Test environment prepared for "should respond to GraphQL introspection query"
[2025-06-21T11:04:41.052Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:41.052Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:41.052Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:41.052Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-21T11:04:41.052Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-21T11:04:41.052Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:41.052Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:41.052Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:41.052Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:41.052Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:41.052Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:41.052Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:41.052Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:41.052Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:41.052Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:41.052Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T11:04:41.052Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T11:04:41.052Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:41.052Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:41.052Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:41.053Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:41.053Z]   [CASE END] - Duration: 59ms
[2025-06-21T11:04:41.053Z] 
  [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-21T11:04:41.053Z]   Module: graphql
[2025-06-21T11:04:41.053Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-21T11:04:41.080Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T11:04:41.080Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:41.080Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:41.080Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:41.080Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T11:04:41.080Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-21T11:04:41.080Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:41.081Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:41.081Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:41.081Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:41.081Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:41.081Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:41.081Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:41.081Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:41.081Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:41.081Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:41.081Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:41.081Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:41.081Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:41.081Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:41.081Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:41.081Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:41.081Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:04:41.082Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T11:04:41.082Z] Suite Duration: 1557ms
[2025-06-21T11:04:41.082Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:04:41.082Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T11:04:41.082Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:41.805Z] 
  [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-21T11:04:41.805Z]   Module: graphql
[2025-06-21T11:04:41.805Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-21T11:04:41.860Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T11:04:41.860Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:41.860Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:41.860Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:41.860Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T11:04:41.860Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:04:41.860Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:41.860Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:41.861Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:41.861Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:41.861Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:41.861Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:41.861Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:41.861Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:41.861Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:41.861Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:41.861Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:41.861Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:41.861Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:41.861Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:41.861Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:41.861Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:41.861Z]   [CASE END] - Duration: 55ms
[2025-06-21T11:04:41.861Z] 
  [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-21T11:04:41.861Z]   Module: graphql
[2025-06-21T11:04:41.861Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-21T11:04:41.885Z]   [Arrange] - Precondition: Test environment prepared for "should support schema introspection"
[2025-06-21T11:04:41.885Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:41.885Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:41.885Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:41.885Z]   [Act] - Step: Executing test logic for "should support schema introspection"
[2025-06-21T11:04:41.885Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:04:41.885Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:41.885Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:41.885Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:41.885Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:41.885Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:41.885Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:41.885Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:41.885Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:41.886Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:41.886Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:41.886Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T11:04:41.886Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T11:04:41.886Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:41.886Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:41.886Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:41.886Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:41.886Z]   [CASE END] - Duration: 24ms
[2025-06-21T11:04:41.886Z] 
  [CASE START] - TC-6C7A3797: should list available queries
[2025-06-21T11:04:41.886Z]   Module: graphql
[2025-06-21T11:04:41.886Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-21T11:04:41.910Z]   [Arrange] - Precondition: Test environment prepared for "should list available queries"
[2025-06-21T11:04:41.910Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:41.910Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:41.910Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:41.910Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-21T11:04:41.910Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:04:41.910Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:41.910Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:41.910Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:41.910Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:41.910Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:41.910Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:41.910Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:41.910Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:41.910Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:41.910Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:41.910Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T11:04:41.910Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T11:04:41.910Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:41.910Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:41.910Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:41.910Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:41.910Z]   [CASE END] - Duration: 24ms
[2025-06-21T11:04:41.910Z] 
  [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-21T11:04:41.911Z]   Module: graphql
[2025-06-21T11:04:41.911Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-21T11:04:41.935Z]   [Arrange] - Precondition: Test environment prepared for "should list available mutations"
[2025-06-21T11:04:41.935Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:41.935Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:41.935Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:41.935Z]   [Act] - Step: Executing test logic for "should list available mutations"
[2025-06-21T11:04:41.935Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:04:41.935Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:41.935Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:41.935Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:41.935Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:41.935Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:41.935Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:41.935Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:41.935Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:41.935Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:41.935Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:41.935Z]   [Assert Log] - Expected result: Test-specific expected behavior
[2025-06-21T11:04:41.935Z]   [Assert Log] - Actual result: Test behavior matched expectations
[2025-06-21T11:04:41.936Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:41.936Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:41.936Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:41.936Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:41.936Z]   [CASE END] - Duration: 25ms
[2025-06-21T11:04:41.937Z] 
  [CASE START] - TC-387C915A: should respond to GraphQL endpoint
[2025-06-21T11:04:41.937Z]   Module: graphql
[2025-06-21T11:04:41.937Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should respond to GraphQL endpoint
[2025-06-21T11:04:41.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:41.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:41.937Z] 
  [CASE START] - TC-21545EE6: should handle invalid GraphQL queries
[2025-06-21T11:04:41.937Z]   Module: graphql
[2025-06-21T11:04:41.937Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should handle invalid GraphQL queries
[2025-06-21T11:04:41.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:41.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:41.937Z] 
  [CASE START] - TC-18BF1AAD: should handle simple queries
[2025-06-21T11:04:41.937Z]   Module: graphql
[2025-06-21T11:04:41.937Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle simple queries
[2025-06-21T11:04:41.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:41.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:41.937Z] 
  [CASE START] - TC-14898FE1: should validate GraphQL syntax
[2025-06-21T11:04:41.937Z]   Module: graphql
[2025-06-21T11:04:41.937Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should validate GraphQL syntax
[2025-06-21T11:04:41.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:41.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:41.937Z] 
  [CASE START] - TC-63BF36C2: should handle empty queries
[2025-06-21T11:04:41.937Z]   Module: graphql
[2025-06-21T11:04:41.937Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle empty queries
[2025-06-21T11:04:41.937Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:41.937Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:41.937Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T11:04:41.937Z] Suite Duration: 846ms
[2025-06-21T11:04:41.937Z] Suite Results: 4 passed, 0 failed, 5 skipped
[2025-06-21T11:04:41.937Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T11:04:41.937Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:42.744Z] 
  [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-21T11:04:42.744Z]   Module: graphql
[2025-06-21T11:04:42.744Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-21T11:04:42.797Z]   [Arrange] - Precondition: Test environment prepared for "should have Customer type in schema"
[2025-06-21T11:04:42.797Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:42.798Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:42.798Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:42.798Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-21T11:04:42.798Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T11:04:42.798Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:42.798Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:42.798Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:42.798Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:42.798Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:42.798Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:42.798Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:42.798Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:42.798Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:42.798Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:42.798Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:42.798Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:42.798Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:42.798Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:42.798Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:42.798Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:42.798Z]   [CASE END] - Duration: 54ms
[2025-06-21T11:04:42.798Z] 
  [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-21T11:04:42.798Z]   Module: graphql
[2025-06-21T11:04:42.798Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-21T11:04:42.821Z]   [Arrange] - Precondition: Test environment prepared for "should have Address type in schema"
[2025-06-21T11:04:42.821Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:42.821Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:42.821Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:42.821Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-21T11:04:42.821Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T11:04:42.821Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:42.821Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:42.821Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:42.821Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:42.821Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:42.821Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:42.821Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:42.821Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:42.821Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:42.821Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:42.821Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:42.821Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:42.821Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:42.821Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:42.821Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:42.821Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:42.821Z]   [CASE END] - Duration: 23ms
[2025-06-21T11:04:42.822Z] 
  [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-21T11:04:42.822Z]   Module: graphql
[2025-06-21T11:04:42.822Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-21T11:04:42.847Z]   [Arrange] - Precondition: Test environment prepared for "should have AddressInput type in schema"
[2025-06-21T11:04:42.847Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:42.847Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:42.847Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:42.847Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-21T11:04:42.847Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T11:04:42.847Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:42.847Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:42.847Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:42.847Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:42.847Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:42.847Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:42.847Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:42.847Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:42.847Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:42.847Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:42.847Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:42.847Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:42.848Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:42.848Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:42.848Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:42.848Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:42.848Z]   [CASE END] - Duration: 25ms
[2025-06-21T11:04:42.849Z] 
  [CASE START] - TC-2C17DA72: should validate customer-related mutations exist
[2025-06-21T11:04:42.849Z]   Module: graphql
[2025-06-21T11:04:42.849Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should validate customer-related mutations exist
[2025-06-21T11:04:42.849Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:42.849Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:42.849Z] 
  [CASE START] - TC-D206B8: should handle GraphQL validation errors
[2025-06-21T11:04:42.849Z]   Module: graphql
[2025-06-21T11:04:42.849Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should handle GraphQL validation errors
[2025-06-21T11:04:42.849Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:04:42.849Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:04:42.849Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T11:04:42.849Z] Suite Duration: 904ms
[2025-06-21T11:04:42.849Z] Suite Results: 3 passed, 0 failed, 2 skipped
[2025-06-21T11:04:42.849Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T11:04:42.849Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:43.566Z] 
  [CASE START] - TC-7DB19ADA: should have order subscription in schema
[2025-06-21T11:04:43.566Z]   Module: graphql
[2025-06-21T11:04:43.566Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have order subscription in schema
[2025-06-21T11:04:43.626Z]   [Arrange] - Precondition: Test environment prepared for "should have order subscription in schema"
[2025-06-21T11:04:43.626Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:43.626Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:43.626Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:43.626Z]   [Act] - Step: Executing test logic for "should have order subscription in schema"
[2025-06-21T11:04:43.626Z]   [Act Log] - Loading target module: /test/integration/order/orderNotifications.js
[2025-06-21T11:04:43.626Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:43.626Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:43.626Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:43.626Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:43.626Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:43.626Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:43.626Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:43.626Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:43.626Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:43.626Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:43.626Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:43.626Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:43.626Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:43.627Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:43.627Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:43.627Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:43.627Z]   [CASE END] - Duration: 60ms
[2025-06-21T11:04:43.627Z] 
  [CASE START] - TC-7B992587: should have mutation types in schema
[2025-06-21T11:04:43.627Z]   Module: graphql
[2025-06-21T11:04:43.627Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have mutation types in schema
[2025-06-21T11:04:43.653Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T11:04:43.653Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:43.653Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:43.653Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:43.653Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T11:04:43.653Z]   [Act Log] - Loading target module: /test/integration/order/orderNotifications.js
[2025-06-21T11:04:43.653Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:43.653Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:43.653Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:43.653Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:43.653Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:43.653Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:43.653Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:43.653Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:43.653Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:43.653Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:43.653Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:43.653Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:43.653Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:43.653Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:43.653Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:43.653Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:43.653Z]   [CASE END] - Duration: 25ms
[2025-06-21T11:04:43.654Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T11:04:43.654Z] Suite Duration: 801ms
[2025-06-21T11:04:43.654Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:04:43.654Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T11:04:43.654Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:44.535Z] 
  [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-21T11:04:44.535Z]   Module: graphql
[2025-06-21T11:04:44.535Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T11:04:44.598Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T11:04:44.598Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:44.598Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:44.598Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:44.598Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T11:04:44.598Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-21T11:04:44.598Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:44.598Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:44.598Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:44.598Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:44.598Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:44.598Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:44.598Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:44.598Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:44.598Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:44.598Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:44.598Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:44.598Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:44.598Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:44.598Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:44.598Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:44.598Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:44.598Z]   [CASE END] - Duration: 62ms
[2025-06-21T11:04:44.598Z] 
  [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-21T11:04:44.599Z]   Module: graphql
[2025-06-21T11:04:44.599Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-21T11:04:44.626Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T11:04:44.626Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:44.626Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:44.626Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:44.626Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T11:04:44.626Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-21T11:04:44.626Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:44.626Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:44.626Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:44.626Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:44.626Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:44.626Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:44.626Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:44.626Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:44.626Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:44.626Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:44.626Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:44.626Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:44.626Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:44.626Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:44.626Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:44.626Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:44.626Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:04:44.627Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T11:04:44.627Z] Suite Duration: 966ms
[2025-06-21T11:04:44.627Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:04:44.628Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T11:04:44.628Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:45.365Z] 
  [CASE START] - TC-50629841: should have updateOrderStatus mutation in schema
[2025-06-21T11:04:45.365Z]   Module: graphql
[2025-06-21T11:04:45.365Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-21T11:04:45.427Z]   [Arrange] - Precondition: Test environment prepared for "should have updateOrderStatus mutation in schema"
[2025-06-21T11:04:45.427Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:45.427Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:45.427Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:45.427Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-21T11:04:45.427Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T11:04:45.427Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:45.427Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:45.427Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:45.427Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:45.427Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:45.427Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:45.427Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:45.427Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:45.427Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:45.427Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:45.427Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:45.427Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:45.427Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:45.427Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:45.427Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:45.428Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:45.428Z]   [CASE END] - Duration: 62ms
[2025-06-21T11:04:45.428Z] 
  [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-21T11:04:45.428Z]   Module: graphql
[2025-06-21T11:04:45.428Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-21T11:04:45.455Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type with orderStatus field in schema"
[2025-06-21T11:04:45.455Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:45.455Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:45.455Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:45.455Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-21T11:04:45.455Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T11:04:45.455Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:45.455Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:45.455Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:45.455Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:45.455Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:45.455Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:45.455Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:45.455Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:45.455Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:45.455Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:45.455Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:45.455Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:45.455Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:45.455Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:45.455Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:45.455Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:45.455Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:04:45.455Z] 
  [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-21T11:04:45.456Z]   Module: graphql
[2025-06-21T11:04:45.456Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-21T11:04:45.482Z]   [Arrange] - Precondition: Test environment prepared for "should have order status enum values in schema"
[2025-06-21T11:04:45.482Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:45.482Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:45.482Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:45.482Z]   [Act] - Step: Executing test logic for "should have order status enum values in schema"
[2025-06-21T11:04:45.482Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T11:04:45.482Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:45.482Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:45.482Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:45.482Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:45.482Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:45.482Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:45.482Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:45.482Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:45.482Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:45.482Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:45.482Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:45.482Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:45.482Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:45.482Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:45.483Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:45.483Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:45.483Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:04:45.484Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T11:04:45.484Z] Suite Duration: 852ms
[2025-06-21T11:04:45.484Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-21T11:04:45.484Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T11:04:45.484Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:46.345Z] 
  [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-21T11:04:46.345Z]   Module: graphql
[2025-06-21T11:04:46.345Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T11:04:46.431Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T11:04:46.432Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:46.432Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:46.432Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:46.432Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T11:04:46.432Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-21T11:04:46.432Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:46.432Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:46.432Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:46.432Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:46.432Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:46.432Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:46.432Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:46.432Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:46.432Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:46.432Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:46.432Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:46.432Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:46.432Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:46.432Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:46.432Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:46.432Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:46.432Z]   [CASE END] - Duration: 86ms
[2025-06-21T11:04:46.432Z] 
  [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-21T11:04:46.432Z]   Module: graphql
[2025-06-21T11:04:46.432Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-21T11:04:46.475Z]   [Arrange] - Precondition: Test environment prepared for "should have query types in schema"
[2025-06-21T11:04:46.475Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:46.475Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:46.475Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:46.475Z]   [Act] - Step: Executing test logic for "should have query types in schema"
[2025-06-21T11:04:46.475Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-21T11:04:46.475Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:46.475Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:46.475Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:46.475Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:46.475Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:46.475Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:46.475Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:46.475Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:46.475Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:46.475Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:46.475Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:46.475Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:46.475Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:46.475Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:46.475Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:46.475Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:46.475Z]   [CASE END] - Duration: 42ms
[2025-06-21T11:04:46.477Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T11:04:46.477Z] Suite Duration: 985ms
[2025-06-21T11:04:46.477Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:04:46.477Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T11:04:46.477Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:47.250Z] 
  [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-21T11:04:47.250Z]   Module: order
[2025-06-21T11:04:47.250Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-21T11:04:47.307Z]   [Arrange] - Precondition: Test environment prepared for "should have order query in schema"
[2025-06-21T11:04:47.308Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:47.308Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:47.308Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:47.308Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-21T11:04:47.308Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-21T11:04:47.308Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:47.308Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:47.308Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:47.308Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:47.308Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:47.308Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:47.308Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:47.308Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:47.308Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:47.308Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:47.308Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:47.308Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:47.308Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:47.308Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:47.308Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:47.308Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:47.308Z]   [CASE END] - Duration: 57ms
[2025-06-21T11:04:47.308Z] 
  [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-21T11:04:47.308Z]   Module: order
[2025-06-21T11:04:47.308Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-21T11:04:47.339Z]   [Arrange] - Precondition: Test environment prepared for "should have orders query in schema"
[2025-06-21T11:04:47.339Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:47.339Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:47.339Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:47.339Z]   [Act] - Step: Executing test logic for "should have orders query in schema"
[2025-06-21T11:04:47.339Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-21T11:04:47.339Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:47.339Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:47.339Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:47.339Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:47.339Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:47.339Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:47.339Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:47.339Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:47.339Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:47.339Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:47.339Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:47.339Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:47.339Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:47.339Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:47.339Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:47.339Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:47.339Z]   [CASE END] - Duration: 30ms
[2025-06-21T11:04:47.340Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T11:04:47.340Z] Suite Duration: 855ms
[2025-06-21T11:04:47.340Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:04:47.340Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T11:04:47.340Z] Suite Display Name: INTEGRATION
[2025-06-21T11:04:48.062Z] 
  [CASE START] - TC-528B5677: should have payment related types in schema
[2025-06-21T11:04:48.062Z]   Module: graphql
[2025-06-21T11:04:48.062Z]   Full Path: Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T11:04:48.125Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T11:04:48.125Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:04:48.125Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:04:48.125Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:04:48.125Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T11:04:48.125Z]   [Act Log] - Loading target module: /test/integration/payment/paymentSystem.js
[2025-06-21T11:04:48.125Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:04:48.125Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:04:48.125Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:04:48.125Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:04:48.125Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:04:48.125Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:04:48.125Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:04:48.125Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:04:48.125Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:04:48.125Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:04:48.125Z]   [Assert Log] - Expected result: Property/Method exists (defined)
[2025-06-21T11:04:48.125Z]   [Assert Log] - Actual result: Property/Method found and accessible
[2025-06-21T11:04:48.125Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:04:48.125Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:04:48.125Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:04:48.125Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:04:48.125Z]   [CASE END] - Duration: 63ms
[2025-06-21T11:04:48.126Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T11:04:48.126Z] Suite Duration: 779ms
[2025-06-21T11:04:48.126Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T11:04:48.136Z] 
================================================================================
[2025-06-21T11:04:48.137Z] Test Run Finished
[2025-06-21T11:04:48.137Z] End Timestamp: 2025-06-21T11:04:48.136Z
[2025-06-21T11:04:48.137Z] Total Duration: 15713ms (15.71s)
[2025-06-21T11:04:48.137Z] 
[STATISTICS]
[2025-06-21T11:04:48.137Z] Total Test Suites: 14
[2025-06-21T11:04:48.137Z] Passed Test Suites: 10
[2025-06-21T11:04:48.137Z] Failed Test Suites: 0
[2025-06-21T11:04:48.137Z] Total Tests: 52
[2025-06-21T11:04:48.137Z] Passed Tests: 24
[2025-06-21T11:04:48.137Z] Failed Tests: 0
[2025-06-21T11:04:48.137Z] Skipped Tests: 28
[2025-06-21T11:04:48.137Z] Success Rate: 46.15%
[2025-06-21T11:04:48.137Z] Overall Result: FAILURE
[2025-06-21T11:04:48.137Z] ================================================================================
