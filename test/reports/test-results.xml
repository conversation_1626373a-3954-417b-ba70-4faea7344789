<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Firespoon API Tests" tests="52" failures="0" errors="0" time="14.598">
  <testsuite name="WhatsApp Webhook Integration" errors="0" failures="0" skipped="3" timestamp="2025-06-21T11:08:40" time="2.259" tests="3">
    <testcase classname="INTEGRATION.WhatsApp Webhook Integration" name="should process incoming message and create session" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.WhatsApp Webhook Integration" name="should reject webhook with invalid signature" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.WhatsApp Webhook Integration" name="should handle malformed webhook payload" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Refund System Integration Tests" errors="0" failures="0" skipped="2" timestamp="2025-06-21T11:08:42" time="0.135" tests="2">
    <testcase classname="INTEGRATION.Refund System Integration Tests › refundStatus Default Value Tests" name="should verify refundStatus default value is NONE" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Refund System Integration Tests › refundStatus Default Value Tests" name="should verify refund status enum values" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Real Stripe Payment Integration Tests" errors="0" failures="0" skipped="9" timestamp="2025-06-21T11:08:42" time="1.05" tests="9">
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should create real payment intent with Stripe" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should retrieve real payment intent status" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should handle payment with test card" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should handle declined card" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Customer Management" name="should create real Stripe customer" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Customer Management" name="should retrieve real Stripe customer" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Webhook Processing" name="should process real webhook signature validation" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Error Handling with Real API" name="should handle invalid amount" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Error Handling with Real API" name="should handle invalid currency" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL Mutations" errors="0" failures="0" skipped="4" timestamp="2025-06-21T11:08:43" time="0.77" tests="4">
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should create a new order" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should fail to create order with invalid restaurant ID" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should fail to create order without authentication" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should update order status" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL API Integration Tests" errors="0" failures="0" skipped="3" timestamp="2025-06-21T11:08:44" time="1.688" tests="6">
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Schema Types" name="should have Order type in schema" time="0.303">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Schema Types" name="should have OrderStatus enum in schema" time="0.032">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Input Types" name="should have OrderInput type in schema" time="0.029">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Input Types" name="should validate GraphQL order operations" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › GraphQL Error Handling" name="should handle malformed order queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › GraphQL Error Handling" name="should validate required arguments" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="GraphQL Queries Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:46" time="1.385" tests="2">
    <testcase classname="INTEGRATION.GraphQL Queries Integration Tests › GraphQL Schema Introspection" name="should respond to GraphQL introspection query" time="0.063">
    </testcase>
    <testcase classname="INTEGRATION.GraphQL Queries Integration Tests › GraphQL Schema Introspection" name="should have Restaurant type in schema" time="0.028">
    </testcase>
  </testsuite>
  <testsuite name="PayPal Payment Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:47" time="0.925" tests="2">
    <testcase classname="INTEGRATION.PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have payment related types in schema" time="0.055">
    </testcase>
    <testcase classname="INTEGRATION.PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have query types in schema" time="0.027">
    </testcase>
  </testsuite>
  <testsuite name="Stripe Payment Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:48" time="1.115" tests="2">
    <testcase classname="INTEGRATION.Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have payment related types in schema" time="0.084">
    </testcase>
    <testcase classname="INTEGRATION.Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have mutation types in schema" time="0.048">
    </testcase>
  </testsuite>
  <testsuite name="Customer GraphQL API Integration Tests" errors="0" failures="0" skipped="2" timestamp="2025-06-21T11:08:49" time="1.053" tests="5">
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Schema Types" name="should have Customer type in schema" time="0.061">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Schema Types" name="should have Address type in schema" time="0.034">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Input Types" name="should have AddressInput type in schema" time="0.035">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Operations" name="should validate customer-related mutations exist" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Operations" name="should handle GraphQL validation errors" time="0">
      <skipped/>
    </testcase>
  </testsuite>
  <testsuite name="Order Management Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:50" time="0.89" tests="2">
    <testcase classname="INTEGRATION.Order Management Integration Tests › Order GraphQL Schema Tests" name="should have order query in schema" time="0.057">
    </testcase>
    <testcase classname="INTEGRATION.Order Management Integration Tests › Order GraphQL Schema Tests" name="should have orders query in schema" time="0.027">
    </testcase>
  </testsuite>
  <testsuite name="Order State Machine Integration Tests (GraphQL)" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:51" time="0.818" tests="3">
    <testcase classname="INTEGRATION.Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates" name="should have updateOrderStatus mutation in schema" time="0.059">
    </testcase>
    <testcase classname="INTEGRATION.Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates" name="should have Order type with orderStatus field in schema" time="0.024">
    </testcase>
    <testcase classname="INTEGRATION.Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates" name="should have order status enum values in schema" time="0.025">
    </testcase>
  </testsuite>
  <testsuite name="Restaurant GraphQL API Integration Tests" errors="0" failures="0" skipped="5" timestamp="2025-06-21T11:08:52" time="0.815" tests="9">
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › GraphQL Endpoint" name="should respond to GraphQL endpoint" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › GraphQL Endpoint" name="should handle invalid GraphQL queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Restaurant Schema" name="should have Restaurant type in schema" time="0.056">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Basic GraphQL Operations" name="should handle simple queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Basic GraphQL Operations" name="should validate GraphQL syntax" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Basic GraphQL Operations" name="should handle empty queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Schema Introspection" name="should support schema introspection" time="0.025">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Schema Introspection" name="should list available queries" time="0.024">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Schema Introspection" name="should list available mutations" time="0.027">
    </testcase>
  </testsuite>
  <testsuite name="Order Notifications Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:53" time="0.875" tests="2">
    <testcase classname="INTEGRATION.Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests" name="should have order subscription in schema" time="0.055">
    </testcase>
    <testcase classname="INTEGRATION.Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests" name="should have mutation types in schema" time="0.025">
    </testcase>
  </testsuite>
  <testsuite name="Payment System Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-21T11:08:54" time="0.742" tests="1">
    <testcase classname="INTEGRATION.Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have payment related types in schema" time="0.06">
    </testcase>
  </testsuite>
</testsuites>