[2025-06-21T11:03:20.101Z] ================================================================================
[2025-06-21T11:03:20.101Z] Test Run Started
[2025-06-21T11:03:20.101Z] Timestamp: 2025-06-21T11:03:20.101Z
[2025-06-21T11:03:20.101Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:03:20.101Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:03:20.101Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:03:20.101Z] ================================================================================
[2025-06-21T11:03:20.106Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:03:20.107Z] Total Test Suites: 12
[2025-06-21T11:03:20.107Z] Test Environment: test
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:03:20.202Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.202Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:03:20.203Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.203Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:03:20.203Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.203Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:03:20.203Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.203Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:03:20.203Z] Suite Display Name: UNIT
[2025-06-21T11:03:20.203Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:03:20.203Z] Suite Display Name: UNIT
[2025-06-21T11:03:22.000Z] 
  [CASE START] - TC-42BBFCF8: should export a service instance
[2025-06-21T11:03:22.000Z]   Module: sessionservice
[2025-06-21T11:03:22.000Z]   Full Path: SessionService › Service Initialization › should export a service instance
[2025-06-21T11:03:22.000Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.000Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.000Z] 
  [CASE START] - TC-55708206: should have required methods
[2025-06-21T11:03:22.000Z]   Module: sessionservice
[2025-06-21T11:03:22.000Z]   Full Path: SessionService › Service Initialization › should have required methods
[2025-06-21T11:03:22.000Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.000Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.000Z] 
  [CASE START] - TC-432E6A85: should have Redis client
[2025-06-21T11:03:22.001Z]   Module: sessionservice
[2025-06-21T11:03:22.001Z]   Full Path: SessionService › Service Initialization › should have Redis client
[2025-06-21T11:03:22.001Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.001Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.001Z] 
  [CASE START] - TC-66717DE3: should have session queues map
[2025-06-21T11:03:22.001Z]   Module: sessionservice
[2025-06-21T11:03:22.001Z]   Full Path: SessionService › Service Initialization › should have session queues map
[2025-06-21T11:03:22.001Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.001Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.001Z] 
  [CASE START] - TC-7E1E9FDB: should be able to call initializeContext method
[2025-06-21T11:03:22.001Z]   Module: sessionservice
[2025-06-21T11:03:22.001Z]   Full Path: SessionService › Basic Functionality › should be able to call initializeContext method
[2025-06-21T11:03:22.001Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.001Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.001Z] 
  [CASE START] - TC-FAE0200: should be able to call createSession method
[2025-06-21T11:03:22.001Z]   Module: sessionservice
[2025-06-21T11:03:22.001Z]   Full Path: SessionService › Basic Functionality › should be able to call createSession method
[2025-06-21T11:03:22.002Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.002Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.002Z] 
  [CASE START] - TC-57DCE908: should be able to call getSession method
[2025-06-21T11:03:22.002Z]   Module: sessionservice
[2025-06-21T11:03:22.002Z]   Full Path: SessionService › Basic Functionality › should be able to call getSession method
[2025-06-21T11:03:22.002Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.002Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.002Z] 
  [CASE START] - TC-263394F3: should be able to call updateSession method
[2025-06-21T11:03:22.002Z]   Module: sessionservice
[2025-06-21T11:03:22.002Z]   Full Path: SessionService › Basic Functionality › should be able to call updateSession method
[2025-06-21T11:03:22.002Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.002Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.002Z] 
  [CASE START] - TC-8F1846F: should be able to call deleteSession method
[2025-06-21T11:03:22.002Z]   Module: sessionservice
[2025-06-21T11:03:22.002Z]   Full Path: SessionService › Basic Functionality › should be able to call deleteSession method
[2025-06-21T11:03:22.002Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.002Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.002Z] 
  [CASE START] - TC-6A8290B9: should initialize context with proper structure
[2025-06-21T11:03:22.002Z]   Module: sessionservice
[2025-06-21T11:03:22.002Z]   Full Path: SessionService › Context Initialization › should initialize context with proper structure
[2025-06-21T11:03:22.002Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.002Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.002Z] 
  [CASE START] - TC-62860ACF: should merge custom context with defaults
[2025-06-21T11:03:22.003Z]   Module: sessionservice
[2025-06-21T11:03:22.003Z]   Full Path: SessionService › Context Initialization › should merge custom context with defaults
[2025-06-21T11:03:22.003Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.003Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.003Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/sessionService.test.js
[2025-06-21T11:03:22.003Z] Suite Duration: 1556ms
[2025-06-21T11:03:22.003Z] Suite Results: 0 passed, 0 failed, 11 skipped
[2025-06-21T11:03:22.005Z] 
  [CASE START] - TC-18E6956D: should export a service instance
[2025-06-21T11:03:22.005Z]   Module: restaurantstore
[2025-06-21T11:03:22.005Z]   Full Path: RestaurantStore › Service Initialization › should export a service instance
[2025-06-21T11:03:22.005Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.005Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.005Z] 
  [CASE START] - TC-E1E0EEB: should have required methods
[2025-06-21T11:03:22.006Z]   Module: restaurantstore
[2025-06-21T11:03:22.006Z]   Full Path: RestaurantStore › Service Initialization › should have required methods
[2025-06-21T11:03:22.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.006Z] 
  [CASE START] - TC-56CD7A58: should have data storage maps
[2025-06-21T11:03:22.006Z]   Module: restaurantstore
[2025-06-21T11:03:22.006Z]   Full Path: RestaurantStore › Service Initialization › should have data storage maps
[2025-06-21T11:03:22.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.006Z] 
  [CASE START] - TC-6750CA93: should be able to call initialize method
[2025-06-21T11:03:22.006Z]   Module: restaurantstore
[2025-06-21T11:03:22.006Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call initialize method
[2025-06-21T11:03:22.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.006Z] 
  [CASE START] - TC-4080D923: should be able to call getBrandRef method
[2025-06-21T11:03:22.006Z]   Module: restaurantstore
[2025-06-21T11:03:22.006Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getBrandRef method
[2025-06-21T11:03:22.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.006Z] 
  [CASE START] - TC-27171E43: should be able to call getRestaurantRef method
[2025-06-21T11:03:22.006Z]   Module: restaurantstore
[2025-06-21T11:03:22.006Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantRef method
[2025-06-21T11:03:22.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.007Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.007Z] 
  [CASE START] - TC-7F70C311: should be able to call getRestaurantsByBrand method
[2025-06-21T11:03:22.007Z]   Module: restaurantstore
[2025-06-21T11:03:22.007Z]   Full Path: RestaurantStore › Basic Functionality › should be able to call getRestaurantsByBrand method
[2025-06-21T11:03:22.007Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.007Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.007Z] 
  [CASE START] - TC-4386F747: should have proper data structure
[2025-06-21T11:03:22.007Z]   Module: restaurantstore
[2025-06-21T11:03:22.007Z]   Full Path: RestaurantStore › Data Storage › should have proper data structure
[2025-06-21T11:03:22.007Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.007Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.007Z] 
  [CASE START] - TC-302DFE5F: should handle empty data gracefully
[2025-06-21T11:03:22.007Z]   Module: restaurantstore
[2025-06-21T11:03:22.007Z]   Full Path: RestaurantStore › Data Storage › should handle empty data gracefully
[2025-06-21T11:03:22.007Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.007Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.007Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/restaurantStore.test.js
[2025-06-21T11:03:22.007Z] Suite Duration: 1521ms
[2025-06-21T11:03:22.007Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:03:22.141Z] 
  [CASE START] - TC-12F12294: should export a service instance
[2025-06-21T11:03:22.141Z]   Module: whatsapp
[2025-06-21T11:03:22.141Z]   Full Path: WhatsAppService › Service Initialization › should export a service instance
[2025-06-21T11:03:22.144Z] 
  [CASE START] - TC-6BA6D2C0: should generate menu link with token and orderURL
[2025-06-21T11:03:22.144Z]   Module: linkgenerator
[2025-06-21T11:03:22.145Z]   Full Path: LinkGenerator › generateMenuLink() › should generate menu link with token and orderURL
[2025-06-21T11:03:22.145Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.145Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.145Z] 
  [CASE START] - TC-C54475: should handle different domains
[2025-06-21T11:03:22.145Z]   Module: linkgenerator
[2025-06-21T11:03:22.145Z]   Full Path: LinkGenerator › generateMenuLink() › should handle different domains
[2025-06-21T11:03:22.145Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.145Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.145Z] 
  [CASE START] - TC-692C773A: should handle special characters in token
[2025-06-21T11:03:22.145Z]   Module: linkgenerator
[2025-06-21T11:03:22.145Z]   Full Path: LinkGenerator › generateMenuLink() › should handle special characters in token
[2025-06-21T11:03:22.145Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.145Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.145Z] 
  [CASE START] - TC-5BFB45D0: should generate address link with token and orderURL
[2025-06-21T11:03:22.145Z]   Module: linkgenerator
[2025-06-21T11:03:22.145Z]   Full Path: LinkGenerator › generateAddressLink() › should generate address link with token and orderURL
[2025-06-21T11:03:22.145Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.145Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.145Z] 
  [CASE START] - TC-62AD90E4: should handle different domains
[2025-06-21T11:03:22.145Z]   Module: linkgenerator
[2025-06-21T11:03:22.145Z]   Full Path: LinkGenerator › generateAddressLink() › should handle different domains
[2025-06-21T11:03:22.146Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.146Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.146Z] 
  [CASE START] - TC-1FFDBD21: should handle special characters in token
[2025-06-21T11:03:22.146Z]   Module: linkgenerator
[2025-06-21T11:03:22.146Z]   Full Path: LinkGenerator › generateAddressLink() › should handle special characters in token
[2025-06-21T11:03:22.146Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.146Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.146Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/linkGenerator.test.js
[2025-06-21T11:03:22.146Z] Suite Duration: 1518ms
[2025-06-21T11:03:22.146Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:03:22.176Z] 
  [CASE START] - TC-4E5EC81D: should build basic text message for notification
[2025-06-21T11:03:22.176Z]   Module: messagebuilders
[2025-06-21T11:03:22.176Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for notification
[2025-06-21T11:03:22.176Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - TC-2EB2B9C0: should build basic text message for dialog
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should build basic text message for dialog
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - ***********: should default to notification message type
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should default to notification message type
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - TC-1AA79168: should handle empty text message
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle empty text message
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - TC-6C937016: should handle special characters in text
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildBasicTextMessageData() › should handle special characters in text
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - TC-6E6FD1E2: should build quick reply message with buttons
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should build quick reply message with buttons
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - TC-4EA7B919: should handle single button
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle single button
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.177Z] 
  [CASE START] - TC-6C3E10FA: should handle header and footer
[2025-06-21T11:03:22.177Z]   Module: messagebuilders
[2025-06-21T11:03:22.177Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should handle header and footer
[2025-06-21T11:03:22.177Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.177Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-4DF75E46: should throw error for invalid button count
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › buildQuickReplyMessageData() › should throw error for invalid button count
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-524BC08C: should build template message with variables
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should build template message with variables
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-70460A45: should handle template with image
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle template with image
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - ***********: should handle empty options
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › buildWhatsAppTemplateMessageData() › should handle empty options
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-98049B8: should build dialogue text message
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should build dialogue text message
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-38C6B04A: should handle empty text
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › buildDialogueMessageData() › should handle empty text
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-5909305E: should create valid notification message structure
[2025-06-21T11:03:22.178Z]   Module: messagebuilders
[2025-06-21T11:03:22.178Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid notification message structure
[2025-06-21T11:03:22.178Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.178Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.178Z] 
  [CASE START] - TC-485FC48B: should create valid dialogue message structure
[2025-06-21T11:03:22.179Z]   Module: messagebuilders
[2025-06-21T11:03:22.179Z]   Full Path: MessageBuilders › Message Structure Validation › should create valid dialogue message structure
[2025-06-21T11:03:22.179Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.179Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.179Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/messageBuilders.test.js
[2025-06-21T11:03:22.179Z] Suite Duration: 1737ms
[2025-06-21T11:03:22.179Z] Suite Results: 0 passed, 0 failed, 16 skipped
[2025-06-21T11:03:22.192Z] 
  [CASE START] - TC-111FB3AD: should generate an opaque token
[2025-06-21T11:03:22.192Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.192Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate an opaque token
[2025-06-21T11:03:22.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.192Z] 
  [CASE START] - TC-4B280629: should generate unique tokens
[2025-06-21T11:03:22.192Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.192Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate unique tokens
[2025-06-21T11:03:22.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.192Z] 
  [CASE START] - TC-332818F5: should generate tokens with base64url format
[2025-06-21T11:03:22.192Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.192Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens with base64url format
[2025-06-21T11:03:22.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.192Z] 
  [CASE START] - TC-63B2BA51: should generate tokens of consistent length
[2025-06-21T11:03:22.192Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.192Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should generate tokens of consistent length
[2025-06-21T11:03:22.192Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.192Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.192Z] 
  [CASE START] - TC-73C6E4FA: should handle errors gracefully
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › generateOpaqueToken() › should handle errors gracefully
[2025-06-21T11:03:22.193Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.193Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.193Z] 
  [CASE START] - TC-19FF4489: should validate a valid token
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › validateToken() › should validate a valid token
[2025-06-21T11:03:22.193Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.193Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.193Z] 
  [CASE START] - TC-3852DCF6: should reject null token
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › validateToken() › should reject null token
[2025-06-21T11:03:22.193Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.193Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.193Z] 
  [CASE START] - TC-57467E13: should reject undefined token
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › validateToken() › should reject undefined token
[2025-06-21T11:03:22.193Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.193Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.193Z] 
  [CASE START] - TC-7E101E80: should reject non-string token
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › validateToken() › should reject non-string token
[2025-06-21T11:03:22.193Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.193Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.193Z] 
  [CASE START] - TC-3F13C1F3: should reject empty string token
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › validateToken() › should reject empty string token
[2025-06-21T11:03:22.193Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.193Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.193Z] 
  [CASE START] - TC-42DBAFA6: should reject invalid base64url token
[2025-06-21T11:03:22.193Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.193Z]   Full Path: SessionIdGenerator › validateToken() › should reject invalid base64url token
[2025-06-21T11:03:22.194Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.194Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.194Z] 
  [CASE START] - TC-6C5233AA: should reject token with wrong length
[2025-06-21T11:03:22.194Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.194Z]   Full Path: SessionIdGenerator › validateToken() › should reject token with wrong length
[2025-06-21T11:03:22.194Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.194Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.194Z] 
  [CASE START] - TC-294A0565: should generate cryptographically secure tokens
[2025-06-21T11:03:22.194Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.194Z]   Full Path: SessionIdGenerator › Token Security › should generate cryptographically secure tokens
[2025-06-21T11:03:22.194Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.194Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.194Z] 
  [CASE START] - TC-1F0FC471: should generate tokens that are URL-safe
[2025-06-21T11:03:22.194Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.194Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens that are URL-safe
[2025-06-21T11:03:22.194Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.194Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.194Z] 
  [CASE START] - TC-6411A1FA: should generate tokens without padding
[2025-06-21T11:03:22.194Z]   Module: sessionidgenerator
[2025-06-21T11:03:22.194Z]   Full Path: SessionIdGenerator › Token Security › should generate tokens without padding
[2025-06-21T11:03:22.194Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.194Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.194Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/utils/sessionIdGenerator.test.js
[2025-06-21T11:03:22.194Z] Suite Duration: 1746ms
[2025-06-21T11:03:22.194Z] Suite Results: 0 passed, 0 failed, 15 skipped
[2025-06-21T11:03:22.305Z]   [Arrange] - Precondition: Test environment prepared for "should export a service instance"
[2025-06-21T11:03:22.305Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:03:22.305Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:03:22.305Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:03:22.305Z]   [Act] - Step: Executing test logic for "should export a service instance"
[2025-06-21T11:03:22.305Z]   [Act Log] - Loading target module: whatsapp/services/whatsappService.js
[2025-06-21T11:03:22.305Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:03:22.305Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:03:22.306Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:03:22.306Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T11:03:22.306Z]   [Act Log] - console: 2 outputs
[2025-06-21T11:03:22.306Z]   [Act Log] - process: 2 outputs
[2025-06-21T11:03:22.306Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:03:22.306Z]   [Act Log] - +104ms [console.log] Console log level: info
[2025-06-21T11:03:22.306Z]   [Act Log] - +104ms [process.stdout] Console log level: info

[2025-06-21T11:03:22.306Z]   [Act Log] - +114ms [console.log] File log level: warn
[2025-06-21T11:03:22.306Z]   [Act Log] - +114ms [process.stdout] File log level: warn

[2025-06-21T11:03:22.306Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:03:22.306Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:03:22.306Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:03:22.306Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:03:22.306Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:03:22.306Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:03:22.306Z]   [Assert Log] - Expected result: Object/Function export (truthy value)
[2025-06-21T11:03:22.307Z]   [Assert Log] - Actual result: Module exported successfully (object/function)
[2025-06-21T11:03:22.307Z]   [Assert Log] - Comparison status: PASSED - Expected === Actual
[2025-06-21T11:03:22.307Z]   [Assert Log] - Variable states: All variables in expected state
[2025-06-21T11:03:22.307Z]   [Assert Log] - Side effects: No unexpected side effects detected
[2025-06-21T11:03:22.307Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:03:22.307Z]   [CASE END] - Duration: 305ms
[2025-06-21T11:03:22.317Z] 
  [CASE START] - TC-1F702E6A: should have required methods
[2025-06-21T11:03:22.317Z]   Module: whatsapp
[2025-06-21T11:03:22.317Z]   Full Path: WhatsAppService › Service Initialization › should have required methods
[2025-06-21T11:03:22.317Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.317Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.317Z] 
  [CASE START] - TC-62301443: should have configuration properties or be configurable
[2025-06-21T11:03:22.317Z]   Module: whatsapp
[2025-06-21T11:03:22.317Z]   Full Path: WhatsAppService › Service Initialization › should have configuration properties or be configurable
[2025-06-21T11:03:22.317Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.317Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.317Z] 
  [CASE START] - TC-31DA8935: should have message queue or queueing capability
[2025-06-21T11:03:22.317Z]   Module: whatsapp
[2025-06-21T11:03:22.317Z]   Full Path: WhatsAppService › Service Initialization › should have message queue or queueing capability
[2025-06-21T11:03:22.317Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.317Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.317Z] 
  [CASE START] - TC-2E6D991F: should have retry configuration or error handling
[2025-06-21T11:03:22.317Z]   Module: whatsapp
[2025-06-21T11:03:22.317Z]   Full Path: WhatsAppService › Service Initialization › should have retry configuration or error handling
[2025-06-21T11:03:22.317Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.317Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.317Z] 
  [CASE START] - TC-3309209B: should be able to call getAccessToken method
[2025-06-21T11:03:22.318Z]   Module: whatsapp
[2025-06-21T11:03:22.318Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call getAccessToken method
[2025-06-21T11:03:22.318Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.318Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.318Z] 
  [CASE START] - TC-63754B95: should be able to call sendBasicText method
[2025-06-21T11:03:22.318Z]   Module: whatsapp
[2025-06-21T11:03:22.318Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendBasicText method
[2025-06-21T11:03:22.318Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.318Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.318Z] 
  [CASE START] - TC-1E784041: should be able to call sendQuickReply method
[2025-06-21T11:03:22.318Z]   Module: whatsapp
[2025-06-21T11:03:22.318Z]   Full Path: WhatsAppService › Basic Functionality › should be able to call sendQuickReply method
[2025-06-21T11:03:22.318Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.318Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.318Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js
[2025-06-21T11:03:22.318Z] Suite Duration: 1832ms
[2025-06-21T11:03:22.318Z] Suite Results: 1 passed, 0 failed, 7 skipped
[2025-06-21T11:03:22.702Z] 
  [CASE START] - TC-1409162D: should verify refundStatus field exists in schema
[2025-06-21T11:03:22.702Z]   Module: order
[2025-06-21T11:03:22.702Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus field exists in schema
[2025-06-21T11:03:22.703Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.703Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.703Z] 
  [CASE START] - TC-77F98D2A: should create order with minimal data and verify refundStatus default
[2025-06-21T11:03:22.703Z]   Module: order
[2025-06-21T11:03:22.703Z]   Full Path: Order Model - refundStatus Simple Tests › should create order with minimal data and verify refundStatus default
[2025-06-21T11:03:22.703Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.703Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.703Z] 
  [CASE START] - TC-647025D4: should verify refundStatus can be set to valid enum values
[2025-06-21T11:03:22.703Z]   Module: order
[2025-06-21T11:03:22.703Z]   Full Path: Order Model - refundStatus Simple Tests › should verify refundStatus can be set to valid enum values
[2025-06-21T11:03:22.703Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.703Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.703Z] 
  [CASE START] - TC-2A473732: should verify Order model has all refund-related fields
[2025-06-21T11:03:22.703Z]   Module: order
[2025-06-21T11:03:22.703Z]   Full Path: Order Model - refundStatus Simple Tests › should verify Order model has all refund-related fields
[2025-06-21T11:03:22.703Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.703Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.703Z] 
  [CASE START] - TC-4D61B606: should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:03:22.703Z]   Module: order
[2025-06-21T11:03:22.703Z]   Full Path: Order Model - refundStatus Simple Tests › should demonstrate refundStatus functionality without complex ObjectIds
[2025-06-21T11:03:22.703Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.703Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.703Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.simple.test.js
[2025-06-21T11:03:22.703Z] Suite Duration: 2244ms
[2025-06-21T11:03:22.703Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:03:22.704Z] 
  [CASE START] - TC-3F6DB3EE: should have refundStatus default value as NONE
[2025-06-21T11:03:22.704Z]   Module: order
[2025-06-21T11:03:22.704Z]   Full Path: Order Model - refundStatus Field Tests › should have refundStatus default value as NONE
[2025-06-21T11:03:22.704Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.704Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.704Z] 
  [CASE START] - TC-41D161CE: should allow valid refundStatus enum values
[2025-06-21T11:03:22.704Z]   Module: order
[2025-06-21T11:03:22.704Z]   Full Path: Order Model - refundStatus Field Tests › should allow valid refundStatus enum values
[2025-06-21T11:03:22.704Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.704Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.704Z] 
  [CASE START] - TC-635FEDE6: should reject invalid refundStatus values
[2025-06-21T11:03:22.704Z]   Module: order
[2025-06-21T11:03:22.704Z]   Full Path: Order Model - refundStatus Field Tests › should reject invalid refundStatus values
[2025-06-21T11:03:22.704Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.705Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.705Z] 
  [CASE START] - TC-263635D0: should verify refundStatus field definition in schema
[2025-06-21T11:03:22.705Z]   Module: order
[2025-06-21T11:03:22.705Z]   Full Path: Order Model - refundStatus Field Tests › should verify refundStatus field definition in schema
[2025-06-21T11:03:22.705Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.705Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.705Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.test.js
[2025-06-21T11:03:22.705Z] Suite Duration: 2245ms
[2025-06-21T11:03:22.705Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:03:22.705Z] 
  [CASE START] - TC-15E15FDC: R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:03:22.705Z]   Module: order
[2025-06-21T11:03:22.706Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-004: should update refundStatus to FULL after full refund
[2025-06-21T11:03:22.706Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.706Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.706Z] 
  [CASE START] - TC-421BE5A6: R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:03:22.706Z]   Module: order
[2025-06-21T11:03:22.706Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-006: should support GraphQL refundStatus field queries
[2025-06-21T11:03:22.706Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.706Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.706Z] 
  [CASE START] - TC-7BDF43DE: R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:03:22.706Z]   Module: order
[2025-06-21T11:03:22.706Z]   Full Path: Order Model - refundStatus Advanced Tests › R-STATUS-007: should maintain consistency between refundStatus and totalRefunded
[2025-06-21T11:03:22.706Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.706Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.706Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/models/order.refundStatus.advanced.test.js
[2025-06-21T11:03:22.706Z] Suite Duration: 2235ms
[2025-06-21T11:03:22.706Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:03:22.768Z] 
  [CASE START] - TC-5162C1E9: should send welcome message with restaurant info and buttons
[2025-06-21T11:03:22.768Z]   Module: order
[2025-06-21T11:03:22.768Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should send welcome message with restaurant info and buttons
[2025-06-21T11:03:22.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.768Z] 
  [CASE START] - TC-747E54FA: should handle missing restaurant selection
[2025-06-21T11:03:22.768Z]   Module: order
[2025-06-21T11:03:22.768Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should handle missing restaurant selection
[2025-06-21T11:03:22.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.768Z] 
  [CASE START] - ***********: should throw error with invalid context
[2025-06-21T11:03:22.768Z]   Module: order
[2025-06-21T11:03:22.768Z]   Full Path: Order FSM Actions › sendWelcomeMessage › should throw error with invalid context
[2025-06-21T11:03:22.768Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.768Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.769Z] 
  [CASE START] - TC-36FBC1CD: should successfully send payment link
[2025-06-21T11:03:22.769Z]   Module: order
[2025-06-21T11:03:22.769Z]   Full Path: Order FSM Actions › sendPaymentLink › should successfully send payment link
[2025-06-21T11:03:22.769Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.769Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.769Z] 
  [CASE START] - TC-29E17724: should handle missing parameters
[2025-06-21T11:03:22.769Z]   Module: order
[2025-06-21T11:03:22.769Z]   Full Path: Order FSM Actions › sendPaymentLink › should handle missing parameters
[2025-06-21T11:03:22.769Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.769Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.769Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/orderFsmActions.test.js
[2025-06-21T11:03:22.769Z] Suite Duration: 2310ms
[2025-06-21T11:03:22.769Z] Suite Results: 0 passed, 0 failed, 5 skipped
[2025-06-21T11:03:22.798Z] 
  [CASE START] - TC-1F5838A3: should pass validation with valid token and session
[2025-06-21T11:03:22.798Z]   Module: session
[2025-06-21T11:03:22.798Z]   Full Path: Session Validator Middleware › should pass validation with valid token and session
[2025-06-21T11:03:22.798Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.798Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.798Z] 
  [CASE START] - TC-1A2767BB: should return 401 when token is missing
[2025-06-21T11:03:22.798Z]   Module: session
[2025-06-21T11:03:22.798Z]   Full Path: Session Validator Middleware › should return 401 when token is missing
[2025-06-21T11:03:22.798Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.798Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.798Z] 
  [CASE START] - TC-31A69F55: should return 401 when token format is invalid
[2025-06-21T11:03:22.798Z]   Module: session
[2025-06-21T11:03:22.798Z]   Full Path: Session Validator Middleware › should return 401 when token format is invalid
[2025-06-21T11:03:22.798Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.798Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.798Z] 
  [CASE START] - TC-22D1239E: should return 404 when session does not exist
[2025-06-21T11:03:22.798Z]   Module: session
[2025-06-21T11:03:22.798Z]   Full Path: Session Validator Middleware › should return 404 when session does not exist
[2025-06-21T11:03:22.798Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.798Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.798Z] 
  [CASE START] - TC-EDD952B: should return 404 when session exists but dialogueId is missing
[2025-06-21T11:03:22.798Z]   Module: session
[2025-06-21T11:03:22.798Z]   Full Path: Session Validator Middleware › should return 404 when session exists but dialogueId is missing
[2025-06-21T11:03:22.798Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.798Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.799Z] 
  [CASE START] - TC-685D154: should return 500 when internal error occurs
[2025-06-21T11:03:22.799Z]   Module: session
[2025-06-21T11:03:22.799Z]   Full Path: Session Validator Middleware › should return 500 when internal error occurs
[2025-06-21T11:03:22.799Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:22.799Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:22.799Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/middleware/sessionValidator.test.js
[2025-06-21T11:03:22.799Z] Suite Duration: 2330ms
[2025-06-21T11:03:22.799Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:03:23.252Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T11:03:23.253Z]   Module: dialog
[2025-06-21T11:03:23.253Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T11:03:23.253Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:23.253Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:23.253Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T11:03:23.253Z]   Module: dialog
[2025-06-21T11:03:23.253Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T11:03:23.253Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:23.253Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:23.253Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T11:03:23.253Z]   Module: dialog
[2025-06-21T11:03:23.253Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T11:03:23.253Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:23.253Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:23.253Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T11:03:23.253Z]   Module: dialog
[2025-06-21T11:03:23.253Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T11:03:23.253Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:23.253Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:23.253Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T11:03:23.253Z]   Module: dialog
[2025-06-21T11:03:23.253Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T11:03:23.253Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:23.253Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:23.253Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T11:03:23.253Z]   Module: dialog
[2025-06-21T11:03:23.253Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T11:03:23.253Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:03:23.253Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:03:23.253Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T11:03:23.253Z] Suite Duration: 2796ms
[2025-06-21T11:03:23.253Z] Suite Results: 0 passed, 0 failed, 6 skipped
[2025-06-21T11:03:23.326Z] 
================================================================================
[2025-06-21T11:03:23.326Z] Test Run Finished
[2025-06-21T11:03:23.326Z] End Timestamp: 2025-06-21T11:03:23.326Z
[2025-06-21T11:03:23.326Z] Total Duration: 3225ms (3.23s)
[2025-06-21T11:03:23.326Z] 
[STATISTICS]
[2025-06-21T11:03:23.326Z] Total Test Suites: 12
[2025-06-21T11:03:23.326Z] Passed Test Suites: 1
[2025-06-21T11:03:23.326Z] Failed Test Suites: 0
[2025-06-21T11:03:23.326Z] Total Tests: 94
[2025-06-21T11:03:23.326Z] Passed Tests: 1
[2025-06-21T11:03:23.326Z] Failed Tests: 0
[2025-06-21T11:03:23.326Z] Skipped Tests: 93
[2025-06-21T11:03:23.326Z] Success Rate: 1.06%
[2025-06-21T11:03:23.326Z] Overall Result: FAILURE
[2025-06-21T11:03:23.326Z] ================================================================================
