[2025-06-21T10:46:23.097Z] ================================================================================
[2025-06-21T10:46:23.098Z] Test Run Started
[2025-06-21T10:46:23.098Z] Timestamp: 2025-06-21T10:46:23.097Z
[2025-06-21T10:46:23.098Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:46:23.098Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:46:23.098Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:46:23.098Z] ================================================================================
[2025-06-21T10:46:23.101Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:46:23.101Z] Total Test Suites: 14
[2025-06-21T10:46:23.101Z] Test Environment: test
[2025-06-21T10:46:23.107Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:46:23.107Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:25.539Z] 
  [CASE START] - TC-674469B3: should process incoming message and create session
[2025-06-21T10:46:25.539Z]   Module: whatsapp
[2025-06-21T10:46:25.539Z]   Full Path: WhatsApp Webhook Integration › should process incoming message and create session
[2025-06-21T10:46:25.539Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:25.539Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:25.539Z] 
  [CASE START] - TC-432D970F: should reject webhook with invalid signature
[2025-06-21T10:46:25.539Z]   Module: whatsapp
[2025-06-21T10:46:25.539Z]   Full Path: WhatsApp Webhook Integration › should reject webhook with invalid signature
[2025-06-21T10:46:25.539Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:25.539Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:25.539Z] 
  [CASE START] - TC-3376EAD8: should handle malformed webhook payload
[2025-06-21T10:46:25.539Z]   Module: whatsapp
[2025-06-21T10:46:25.539Z]   Full Path: WhatsApp Webhook Integration › should handle malformed webhook payload
[2025-06-21T10:46:25.539Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:25.539Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:25.539Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:46:25.539Z] Suite Duration: 2415ms
[2025-06-21T10:46:25.539Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:46:25.540Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:46:25.540Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:25.689Z] 
  [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-21T10:46:25.689Z]   Module: refund
[2025-06-21T10:46:25.689Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-21T10:46:25.689Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:25.689Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:25.689Z] 
  [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-21T10:46:25.689Z]   Module: refund
[2025-06-21T10:46:25.689Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-21T10:46:25.689Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:25.689Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:25.689Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:46:25.689Z] Suite Duration: 144ms
[2025-06-21T10:46:25.689Z] Suite Results: 0 passed, 0 failed, 2 skipped
[2025-06-21T10:46:25.689Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:46:25.689Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-2E236918: should create real payment intent with Stripe
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should create real payment intent with Stripe
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-18AE2E8F: should retrieve real payment intent status
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should retrieve real payment intent status
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-6AADE1B7: should handle payment with test card
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle payment with test card
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-54B75689: should handle declined card
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle declined card
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-332A1C6D: should create real Stripe customer
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should create real Stripe customer
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-164683AD: should retrieve real Stripe customer
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should retrieve real Stripe customer
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-5FD44F7E: should process real webhook signature validation
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Real Webhook Processing › should process real webhook signature validation
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-74FBC87B: should handle invalid amount
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid amount
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] 
  [CASE START] - TC-56B8BFFE: should handle invalid currency
[2025-06-21T10:46:26.773Z]   Module: real
[2025-06-21T10:46:26.773Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid currency
[2025-06-21T10:46:26.773Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:26.773Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:26.773Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:46:26.773Z] Suite Duration: 1079ms
[2025-06-21T10:46:26.773Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:46:26.774Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:46:26.774Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:27.546Z] 
  [CASE START] - TC-3CAE5E97: should create a new order
[2025-06-21T10:46:27.546Z]   Module: graphql
[2025-06-21T10:46:27.546Z]   Full Path: Order GraphQL Mutations › should create a new order
[2025-06-21T10:46:27.546Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:27.546Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:27.546Z] 
  [CASE START] - TC-6385F57E: should fail to create order with invalid restaurant ID
[2025-06-21T10:46:27.546Z]   Module: graphql
[2025-06-21T10:46:27.546Z]   Full Path: Order GraphQL Mutations › should fail to create order with invalid restaurant ID
[2025-06-21T10:46:27.546Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:27.546Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:27.546Z] 
  [CASE START] - TC-7D51EFC9: should fail to create order without authentication
[2025-06-21T10:46:27.546Z]   Module: graphql
[2025-06-21T10:46:27.546Z]   Full Path: Order GraphQL Mutations › should fail to create order without authentication
[2025-06-21T10:46:27.546Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:27.546Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:27.546Z] 
  [CASE START] - TC-7912DF71: should update order status
[2025-06-21T10:46:27.546Z]   Module: graphql
[2025-06-21T10:46:27.546Z]   Full Path: Order GraphQL Mutations › should update order status
[2025-06-21T10:46:27.546Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:27.546Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:27.546Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:46:27.546Z] Suite Duration: 768ms
[2025-06-21T10:46:27.546Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:46:27.546Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:46:27.546Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:29.020Z] 
  [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-21T10:46:29.020Z]   Module: graphql
[2025-06-21T10:46:29.020Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-21T10:46:29.289Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:29.289Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-21T10:46:29.289Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:29.289Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:29.289Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:29.289Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:29.289Z]   [CASE END] - Duration: 269ms
[2025-06-21T10:46:29.289Z] 
  [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-21T10:46:29.290Z]   Module: graphql
[2025-06-21T10:46:29.290Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-21T10:46:29.329Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:29.329Z]   [Act] - Step: Executing test logic for "should have OrderStatus enum in schema"
[2025-06-21T10:46:29.329Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:29.329Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:29.329Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:29.329Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:29.329Z]   [CASE END] - Duration: 40ms
[2025-06-21T10:46:29.330Z] 
  [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-21T10:46:29.330Z]   Module: graphql
[2025-06-21T10:46:29.330Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-21T10:46:29.359Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:29.359Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-21T10:46:29.360Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:29.360Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:29.360Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:29.360Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:29.360Z]   [CASE END] - Duration: 29ms
[2025-06-21T10:46:29.362Z] 
  [CASE START] - TC-1EE21EDD: should validate GraphQL order operations
[2025-06-21T10:46:29.362Z]   Module: graphql
[2025-06-21T10:46:29.362Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should validate GraphQL order operations
[2025-06-21T10:46:29.362Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:29.362Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:29.362Z] 
  [CASE START] - TC-8227B16: should handle malformed order queries
[2025-06-21T10:46:29.362Z]   Module: graphql
[2025-06-21T10:46:29.362Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should handle malformed order queries
[2025-06-21T10:46:29.362Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:29.362Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:29.362Z] 
  [CASE START] - TC-C2E23B4: should validate required arguments
[2025-06-21T10:46:29.362Z]   Module: graphql
[2025-06-21T10:46:29.362Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should validate required arguments
[2025-06-21T10:46:29.362Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:29.362Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:29.362Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:46:29.362Z] Suite Duration: 1811ms
[2025-06-21T10:46:29.362Z] Suite Results: 3 passed, 0 failed, 3 skipped
[2025-06-21T10:46:29.362Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:46:29.362Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:30.669Z] 
  [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-21T10:46:30.669Z]   Module: graphql
[2025-06-21T10:46:30.669Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-21T10:46:30.729Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:30.729Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-21T10:46:30.729Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:30.729Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:30.729Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:30.729Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:30.729Z]   [CASE END] - Duration: 60ms
[2025-06-21T10:46:30.730Z] 
  [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-21T10:46:30.730Z]   Module: graphql
[2025-06-21T10:46:30.730Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-21T10:46:30.754Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:30.754Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:46:30.754Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:30.754Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:30.754Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:30.754Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:30.754Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:46:30.755Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:46:30.755Z] Suite Duration: 1390ms
[2025-06-21T10:46:30.755Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:46:30.755Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:46:30.755Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:31.450Z] 
  [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-21T10:46:31.450Z]   Module: order
[2025-06-21T10:46:31.450Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-21T10:46:31.507Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:31.508Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-21T10:46:31.508Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:31.508Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:31.508Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:31.508Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:31.508Z]   [CASE END] - Duration: 58ms
[2025-06-21T10:46:31.508Z] 
  [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-21T10:46:31.508Z]   Module: order
[2025-06-21T10:46:31.508Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-21T10:46:31.538Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:31.538Z]   [Act] - Step: Executing test logic for "should have orders query in schema"
[2025-06-21T10:46:31.538Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:31.538Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:31.538Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:31.538Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:31.538Z]   [CASE END] - Duration: 30ms
[2025-06-21T10:46:31.539Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:46:31.540Z] Suite Duration: 781ms
[2025-06-21T10:46:31.540Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:46:31.540Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:46:31.540Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:32.298Z] 
  [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-21T10:46:32.298Z]   Module: graphql
[2025-06-21T10:46:32.298Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-21T10:46:32.370Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:32.370Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-21T10:46:32.370Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:32.370Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:32.370Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:32.370Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:32.370Z]   [CASE END] - Duration: 71ms
[2025-06-21T10:46:32.370Z] 
  [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-21T10:46:32.370Z]   Module: graphql
[2025-06-21T10:46:32.370Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-21T10:46:32.414Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:32.414Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-21T10:46:32.414Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:32.414Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:32.414Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:32.414Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:32.414Z]   [CASE END] - Duration: 43ms
[2025-06-21T10:46:32.414Z] 
  [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-21T10:46:32.414Z]   Module: graphql
[2025-06-21T10:46:32.414Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-21T10:46:32.446Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:32.446Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-21T10:46:32.446Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:32.446Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:32.446Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:32.446Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:32.446Z]   [CASE END] - Duration: 31ms
[2025-06-21T10:46:32.447Z] 
  [CASE START] - TC-2C17DA72: should validate customer-related mutations exist
[2025-06-21T10:46:32.447Z]   Module: graphql
[2025-06-21T10:46:32.447Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should validate customer-related mutations exist
[2025-06-21T10:46:32.447Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:32.447Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:32.447Z] 
  [CASE START] - TC-D206B8: should handle GraphQL validation errors
[2025-06-21T10:46:32.447Z]   Module: graphql
[2025-06-21T10:46:32.447Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should handle GraphQL validation errors
[2025-06-21T10:46:32.447Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:32.447Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:32.447Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:46:32.447Z] Suite Duration: 905ms
[2025-06-21T10:46:32.447Z] Suite Results: 3 passed, 0 failed, 2 skipped
[2025-06-21T10:46:32.447Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:46:32.447Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:33.125Z] 
  [CASE START] - TC-7DB19ADA: should have order subscription in schema
[2025-06-21T10:46:33.126Z]   Module: graphql
[2025-06-21T10:46:33.126Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have order subscription in schema
[2025-06-21T10:46:33.178Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:33.178Z]   [Act] - Step: Executing test logic for "should have order subscription in schema"
[2025-06-21T10:46:33.178Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:33.178Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:33.178Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:33.178Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:33.178Z]   [CASE END] - Duration: 53ms
[2025-06-21T10:46:33.179Z] 
  [CASE START] - TC-7B992587: should have mutation types in schema
[2025-06-21T10:46:33.179Z]   Module: graphql
[2025-06-21T10:46:33.179Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have mutation types in schema
[2025-06-21T10:46:33.203Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:33.204Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:46:33.204Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:33.204Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:33.204Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:33.204Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:33.204Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:46:33.205Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:46:33.205Z] Suite Duration: 752ms
[2025-06-21T10:46:33.205Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:46:33.205Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:46:33.205Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:33.982Z] 
  [CASE START] - TC-50629841: should have updateOrderStatus mutation in schema
[2025-06-21T10:46:33.982Z]   Module: graphql
[2025-06-21T10:46:33.982Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-21T10:46:34.057Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:34.057Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-21T10:46:34.057Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:34.057Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:34.057Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:34.057Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:34.057Z]   [CASE END] - Duration: 75ms
[2025-06-21T10:46:34.057Z] 
  [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-21T10:46:34.057Z]   Module: graphql
[2025-06-21T10:46:34.057Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-21T10:46:34.081Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:34.081Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-21T10:46:34.081Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:34.081Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:34.081Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:34.081Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:34.081Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:46:34.081Z] 
  [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-21T10:46:34.081Z]   Module: graphql
[2025-06-21T10:46:34.081Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-21T10:46:34.104Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:34.104Z]   [Act] - Step: Executing test logic for "should have order status enum values in schema"
[2025-06-21T10:46:34.104Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:34.104Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:34.104Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:34.104Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:34.104Z]   [CASE END] - Duration: 22ms
[2025-06-21T10:46:34.105Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:46:34.105Z] Suite Duration: 898ms
[2025-06-21T10:46:34.105Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-21T10:46:34.105Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:46:34.105Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:34.848Z] 
  [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-21T10:46:34.848Z]   Module: graphql
[2025-06-21T10:46:34.848Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-21T10:46:34.911Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:34.911Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:46:34.911Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:34.911Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:34.911Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:34.911Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:34.911Z]   [CASE END] - Duration: 64ms
[2025-06-21T10:46:34.911Z] 
  [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-21T10:46:34.911Z]   Module: graphql
[2025-06-21T10:46:34.912Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-21T10:46:34.941Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:34.941Z]   [Act] - Step: Executing test logic for "should support schema introspection"
[2025-06-21T10:46:34.941Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:34.941Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:34.941Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:34.941Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:34.941Z]   [CASE END] - Duration: 30ms
[2025-06-21T10:46:34.941Z] 
  [CASE START] - TC-6C7A3797: should list available queries
[2025-06-21T10:46:34.941Z]   Module: graphql
[2025-06-21T10:46:34.941Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-21T10:46:34.973Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:34.973Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-21T10:46:34.973Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:34.973Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:34.973Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:34.973Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:34.973Z]   [CASE END] - Duration: 31ms
[2025-06-21T10:46:34.973Z] 
  [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-21T10:46:34.973Z]   Module: graphql
[2025-06-21T10:46:34.973Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-21T10:46:35.004Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:35.004Z]   [Act] - Step: Executing test logic for "should list available mutations"
[2025-06-21T10:46:35.004Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:35.004Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:35.004Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:35.004Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:35.004Z]   [CASE END] - Duration: 31ms
[2025-06-21T10:46:35.006Z] 
  [CASE START] - TC-387C915A: should respond to GraphQL endpoint
[2025-06-21T10:46:35.006Z]   Module: graphql
[2025-06-21T10:46:35.006Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should respond to GraphQL endpoint
[2025-06-21T10:46:35.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:35.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:35.006Z] 
  [CASE START] - TC-21545EE6: should handle invalid GraphQL queries
[2025-06-21T10:46:35.006Z]   Module: graphql
[2025-06-21T10:46:35.006Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should handle invalid GraphQL queries
[2025-06-21T10:46:35.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:35.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:35.006Z] 
  [CASE START] - TC-18BF1AAD: should handle simple queries
[2025-06-21T10:46:35.006Z]   Module: graphql
[2025-06-21T10:46:35.006Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle simple queries
[2025-06-21T10:46:35.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:35.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:35.006Z] 
  [CASE START] - TC-14898FE1: should validate GraphQL syntax
[2025-06-21T10:46:35.006Z]   Module: graphql
[2025-06-21T10:46:35.006Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should validate GraphQL syntax
[2025-06-21T10:46:35.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:35.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:35.006Z] 
  [CASE START] - TC-63BF36C2: should handle empty queries
[2025-06-21T10:46:35.006Z]   Module: graphql
[2025-06-21T10:46:35.006Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle empty queries
[2025-06-21T10:46:35.006Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T10:46:35.006Z]   [CASE END] - Duration: 0ms
[2025-06-21T10:46:35.006Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:46:35.006Z] Suite Duration: 896ms
[2025-06-21T10:46:35.006Z] Suite Results: 4 passed, 0 failed, 5 skipped
[2025-06-21T10:46:35.006Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:46:35.007Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:35.794Z] 
  [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-21T10:46:35.794Z]   Module: graphql
[2025-06-21T10:46:35.794Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:46:35.860Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:35.860Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:46:35.860Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:35.860Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:35.860Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:35.860Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:35.860Z]   [CASE END] - Duration: 65ms
[2025-06-21T10:46:35.860Z] 
  [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-21T10:46:35.860Z]   Module: graphql
[2025-06-21T10:46:35.860Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-21T10:46:35.886Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:35.886Z]   [Act] - Step: Executing test logic for "should have query types in schema"
[2025-06-21T10:46:35.886Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:35.886Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:35.886Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:35.886Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:35.886Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:46:35.887Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:46:35.887Z] Suite Duration: 878ms
[2025-06-21T10:46:35.887Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:46:35.887Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:46:35.887Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:36.572Z] 
  [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-21T10:46:36.572Z]   Module: graphql
[2025-06-21T10:46:36.572Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:46:36.630Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:36.630Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:46:36.630Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:36.630Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:36.630Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:36.630Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:36.630Z]   [CASE END] - Duration: 58ms
[2025-06-21T10:46:36.630Z] 
  [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-21T10:46:36.630Z]   Module: graphql
[2025-06-21T10:46:36.630Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-21T10:46:36.656Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:36.656Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:46:36.656Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:36.656Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:36.656Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:36.656Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:36.656Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:46:36.657Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:46:36.657Z] Suite Duration: 767ms
[2025-06-21T10:46:36.657Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:46:36.657Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:46:36.657Z] Suite Display Name: INTEGRATION
[2025-06-21T10:46:37.329Z] 
  [CASE START] - TC-528B5677: should have payment related types in schema
[2025-06-21T10:46:37.329Z]   Module: graphql
[2025-06-21T10:46:37.329Z]   Full Path: Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:46:37.382Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:46:37.382Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:46:37.382Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:46:37.382Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:46:37.382Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:46:37.382Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:46:37.382Z]   [CASE END] - Duration: 53ms
[2025-06-21T10:46:37.383Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:46:37.383Z] Suite Duration: 724ms
[2025-06-21T10:46:37.383Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T10:46:37.392Z] 
================================================================================
[2025-06-21T10:46:37.392Z] Test Run Finished
[2025-06-21T10:46:37.392Z] End Timestamp: 2025-06-21T10:46:37.392Z
[2025-06-21T10:46:37.392Z] Total Duration: 14295ms (14.29s)
[2025-06-21T10:46:37.392Z] 
[STATISTICS]
[2025-06-21T10:46:37.392Z] Total Test Suites: 14
[2025-06-21T10:46:37.392Z] Passed Test Suites: 10
[2025-06-21T10:46:37.392Z] Failed Test Suites: 0
[2025-06-21T10:46:37.392Z] Total Tests: 52
[2025-06-21T10:46:37.392Z] Passed Tests: 24
[2025-06-21T10:46:37.392Z] Failed Tests: 0
[2025-06-21T10:46:37.392Z] Skipped Tests: 28
[2025-06-21T10:46:37.392Z] Success Rate: 46.15%
[2025-06-21T10:46:37.392Z] Overall Result: FAILURE
[2025-06-21T10:46:37.392Z] ================================================================================
