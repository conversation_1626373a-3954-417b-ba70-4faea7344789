[2025-06-21T10:41:48.195Z] ================================================================================
[2025-06-21T10:41:48.196Z] Test Run Started
[2025-06-21T10:41:48.196Z] Timestamp: 2025-06-21T10:41:48.195Z
[2025-06-21T10:41:48.196Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T10:41:48.196Z] Runtime Version: Node.js v18.20.5
[2025-06-21T10:41:48.196Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T10:41:48.196Z] ================================================================================
[2025-06-21T10:41:48.199Z] 
[RUN START] - Test execution beginning
[2025-06-21T10:41:48.199Z] Total Test Suites: 14
[2025-06-21T10:41:48.199Z] Test Environment: test
[2025-06-21T10:41:48.205Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:41:48.205Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:50.364Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T10:41:50.364Z] Suite Duration: 2143ms
[2025-06-21T10:41:50.364Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T10:41:50.364Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:41:50.364Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:50.508Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T10:41:50.508Z] Suite Duration: 138ms
[2025-06-21T10:41:50.508Z] Suite Results: 0 passed, 0 failed, 2 skipped
[2025-06-21T10:41:50.508Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:41:50.508Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:51.530Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T10:41:51.530Z] Suite Duration: 1019ms
[2025-06-21T10:41:51.530Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T10:41:51.531Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:41:51.531Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:52.229Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T10:41:52.229Z] Suite Duration: 695ms
[2025-06-21T10:41:52.229Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T10:41:52.230Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:41:52.230Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:53.454Z] 
  [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-21T10:41:53.455Z]   Module: graphql
[2025-06-21T10:41:53.455Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-21T10:41:53.697Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:53.697Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-21T10:41:53.697Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:53.697Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:53.697Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:53.697Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:53.697Z]   [CASE END] - Duration: 243ms
[2025-06-21T10:41:53.697Z] 
  [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-21T10:41:53.697Z]   Module: graphql
[2025-06-21T10:41:53.697Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-21T10:41:53.731Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:53.731Z]   [Act] - Step: Executing test logic for "should have OrderStatus enum in schema"
[2025-06-21T10:41:53.731Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:53.731Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:53.731Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:53.731Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:53.731Z]   [CASE END] - Duration: 33ms
[2025-06-21T10:41:53.731Z] 
  [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-21T10:41:53.731Z]   Module: graphql
[2025-06-21T10:41:53.731Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-21T10:41:53.756Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:53.757Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-21T10:41:53.757Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:53.757Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:53.757Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:53.757Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:53.757Z]   [CASE END] - Duration: 25ms
[2025-06-21T10:41:53.758Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T10:41:53.758Z] Suite Duration: 1525ms
[2025-06-21T10:41:53.758Z] Suite Results: 3 passed, 0 failed, 3 skipped
[2025-06-21T10:41:53.758Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:41:53.758Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:54.904Z] 
  [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-21T10:41:54.904Z]   Module: graphql
[2025-06-21T10:41:54.904Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-21T10:41:54.954Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:54.954Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-21T10:41:54.954Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:54.954Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:54.954Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:54.954Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:54.954Z]   [CASE END] - Duration: 50ms
[2025-06-21T10:41:54.954Z] 
  [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-21T10:41:54.954Z]   Module: graphql
[2025-06-21T10:41:54.954Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-21T10:41:54.977Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:54.977Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:41:54.977Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:54.977Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:54.977Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:54.977Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:54.977Z]   [CASE END] - Duration: 23ms
[2025-06-21T10:41:54.978Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T10:41:54.978Z] Suite Duration: 1218ms
[2025-06-21T10:41:54.978Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:41:54.978Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:41:54.978Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:55.619Z] 
  [CASE START] - TC-50629841: should have updateOrderStatus mutation in schema
[2025-06-21T10:41:55.620Z]   Module: graphql
[2025-06-21T10:41:55.620Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-21T10:41:55.675Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:55.675Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-21T10:41:55.675Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:55.675Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:55.675Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:55.675Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:55.675Z]   [CASE END] - Duration: 56ms
[2025-06-21T10:41:55.675Z] 
  [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-21T10:41:55.675Z]   Module: graphql
[2025-06-21T10:41:55.675Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-21T10:41:55.698Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:55.698Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-21T10:41:55.698Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:55.698Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:55.698Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:55.698Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:55.698Z]   [CASE END] - Duration: 23ms
[2025-06-21T10:41:55.698Z] 
  [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-21T10:41:55.698Z]   Module: graphql
[2025-06-21T10:41:55.698Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-21T10:41:55.719Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:55.719Z]   [Act] - Step: Executing test logic for "should have order status enum values in schema"
[2025-06-21T10:41:55.719Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:55.719Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:55.719Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:55.719Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:55.719Z]   [CASE END] - Duration: 20ms
[2025-06-21T10:41:55.720Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T10:41:55.720Z] Suite Duration: 738ms
[2025-06-21T10:41:55.720Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-21T10:41:55.720Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:41:55.720Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:56.398Z] 
  [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-21T10:41:56.398Z]   Module: graphql
[2025-06-21T10:41:56.398Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-21T10:41:56.447Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:56.447Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-21T10:41:56.447Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:56.447Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:56.447Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:56.447Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:56.447Z]   [CASE END] - Duration: 49ms
[2025-06-21T10:41:56.447Z] 
  [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-21T10:41:56.447Z]   Module: graphql
[2025-06-21T10:41:56.447Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-21T10:41:56.467Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:56.467Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-21T10:41:56.467Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:56.467Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:56.467Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:56.467Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:56.467Z]   [CASE END] - Duration: 20ms
[2025-06-21T10:41:56.467Z] 
  [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-21T10:41:56.467Z]   Module: graphql
[2025-06-21T10:41:56.467Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-21T10:41:56.487Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:56.487Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-21T10:41:56.487Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:56.487Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:56.488Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:56.488Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:56.488Z]   [CASE END] - Duration: 20ms
[2025-06-21T10:41:56.489Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T10:41:56.489Z] Suite Duration: 766ms
[2025-06-21T10:41:56.489Z] Suite Results: 3 passed, 0 failed, 2 skipped
[2025-06-21T10:41:56.489Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:41:56.489Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:57.093Z] 
  [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-21T10:41:57.093Z]   Module: graphql
[2025-06-21T10:41:57.093Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-21T10:41:57.141Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:57.141Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T10:41:57.141Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:57.141Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:57.141Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:57.141Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:57.141Z]   [CASE END] - Duration: 48ms
[2025-06-21T10:41:57.141Z] 
  [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-21T10:41:57.141Z]   Module: graphql
[2025-06-21T10:41:57.142Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-21T10:41:57.164Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:57.164Z]   [Act] - Step: Executing test logic for "should support schema introspection"
[2025-06-21T10:41:57.164Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:57.164Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:57.164Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:57.164Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:57.164Z]   [CASE END] - Duration: 23ms
[2025-06-21T10:41:57.164Z] 
  [CASE START] - TC-6C7A3797: should list available queries
[2025-06-21T10:41:57.164Z]   Module: graphql
[2025-06-21T10:41:57.164Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-21T10:41:57.186Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:57.186Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-21T10:41:57.186Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:57.186Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:57.186Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:57.186Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:57.186Z]   [CASE END] - Duration: 22ms
[2025-06-21T10:41:57.186Z] 
  [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-21T10:41:57.186Z]   Module: graphql
[2025-06-21T10:41:57.186Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-21T10:41:57.212Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:57.212Z]   [Act] - Step: Executing test logic for "should list available mutations"
[2025-06-21T10:41:57.212Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:57.212Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:57.212Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:57.212Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:57.212Z]   [CASE END] - Duration: 25ms
[2025-06-21T10:41:57.213Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T10:41:57.213Z] Suite Duration: 720ms
[2025-06-21T10:41:57.213Z] Suite Results: 4 passed, 0 failed, 5 skipped
[2025-06-21T10:41:57.213Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:41:57.213Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:57.891Z] 
  [CASE START] - TC-7DB19ADA: should have order subscription in schema
[2025-06-21T10:41:57.891Z]   Module: graphql
[2025-06-21T10:41:57.891Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have order subscription in schema
[2025-06-21T10:41:57.949Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:57.949Z]   [Act] - Step: Executing test logic for "should have order subscription in schema"
[2025-06-21T10:41:57.949Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:57.949Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:57.949Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:57.949Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:57.949Z]   [CASE END] - Duration: 57ms
[2025-06-21T10:41:57.949Z] 
  [CASE START] - TC-7B992587: should have mutation types in schema
[2025-06-21T10:41:57.949Z]   Module: graphql
[2025-06-21T10:41:57.949Z]   Full Path: Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests › should have mutation types in schema
[2025-06-21T10:41:57.972Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:57.972Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:41:57.972Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:57.972Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:57.972Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:57.972Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:57.972Z]   [CASE END] - Duration: 22ms
[2025-06-21T10:41:57.973Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T10:41:57.973Z] Suite Duration: 757ms
[2025-06-21T10:41:57.973Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:41:57.973Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:41:57.973Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:58.582Z] 
  [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-21T10:41:58.582Z]   Module: graphql
[2025-06-21T10:41:58.582Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:41:58.635Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:58.635Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:41:58.635Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:58.635Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:58.635Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:58.635Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:58.635Z]   [CASE END] - Duration: 53ms
[2025-06-21T10:41:58.635Z] 
  [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-21T10:41:58.635Z]   Module: graphql
[2025-06-21T10:41:58.635Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-21T10:41:58.658Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:58.658Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T10:41:58.658Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:58.658Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:58.658Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:58.658Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:58.658Z]   [CASE END] - Duration: 23ms
[2025-06-21T10:41:58.659Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T10:41:58.659Z] Suite Duration: 684ms
[2025-06-21T10:41:58.659Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:41:58.659Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:41:58.659Z] Suite Display Name: INTEGRATION
[2025-06-21T10:41:59.286Z] 
  [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-21T10:41:59.286Z]   Module: graphql
[2025-06-21T10:41:59.286Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:41:59.334Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:59.334Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:41:59.334Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:59.334Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:59.334Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:59.334Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:59.334Z]   [CASE END] - Duration: 48ms
[2025-06-21T10:41:59.334Z] 
  [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-21T10:41:59.334Z]   Module: graphql
[2025-06-21T10:41:59.334Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-21T10:41:59.358Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:41:59.358Z]   [Act] - Step: Executing test logic for "should have query types in schema"
[2025-06-21T10:41:59.358Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:41:59.358Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:41:59.358Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:41:59.358Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:41:59.358Z]   [CASE END] - Duration: 24ms
[2025-06-21T10:41:59.359Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T10:41:59.359Z] Suite Duration: 698ms
[2025-06-21T10:41:59.359Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:41:59.359Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:41:59.359Z] Suite Display Name: INTEGRATION
[2025-06-21T10:42:00.051Z] 
  [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-21T10:42:00.051Z]   Module: order
[2025-06-21T10:42:00.051Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-21T10:42:00.113Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:42:00.113Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-21T10:42:00.113Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:42:00.113Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:42:00.113Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:42:00.113Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:42:00.113Z]   [CASE END] - Duration: 62ms
[2025-06-21T10:42:00.113Z] 
  [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-21T10:42:00.113Z]   Module: order
[2025-06-21T10:42:00.113Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-21T10:42:00.139Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:42:00.139Z]   [Act] - Step: Executing test logic for "should have orders query in schema"
[2025-06-21T10:42:00.139Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:42:00.139Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:42:00.139Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:42:00.139Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:42:00.139Z]   [CASE END] - Duration: 26ms
[2025-06-21T10:42:00.141Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T10:42:00.141Z] Suite Duration: 779ms
[2025-06-21T10:42:00.141Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T10:42:00.141Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:42:00.141Z] Suite Display Name: INTEGRATION
[2025-06-21T10:42:00.754Z] 
  [CASE START] - TC-528B5677: should have payment related types in schema
[2025-06-21T10:42:00.754Z]   Module: graphql
[2025-06-21T10:42:00.754Z]   Full Path: Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T10:42:00.804Z]   [Arrange] - Precondition: Test environment prepared
[2025-06-21T10:42:00.804Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T10:42:00.804Z]   [Act Log] - Execution completed successfully
[2025-06-21T10:42:00.804Z]   [Assert] - Verifying: All assertions passed
[2025-06-21T10:42:00.804Z]   [Assert Log] - Comparison successful: Expected behavior confirmed
[2025-06-21T10:42:00.804Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T10:42:00.804Z]   [CASE END] - Duration: 50ms
[2025-06-21T10:42:00.805Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js
[2025-06-21T10:42:00.805Z] Suite Duration: 661ms
[2025-06-21T10:42:00.805Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T10:42:00.814Z] 
================================================================================
[2025-06-21T10:42:00.814Z] Test Run Finished
[2025-06-21T10:42:00.814Z] End Timestamp: 2025-06-21T10:42:00.814Z
[2025-06-21T10:42:00.814Z] Total Duration: 12619ms (12.62s)
[2025-06-21T10:42:00.814Z] 
[STATISTICS]
[2025-06-21T10:42:00.814Z] Total Test Suites: 14
[2025-06-21T10:42:00.814Z] Passed Test Suites: 10
[2025-06-21T10:42:00.814Z] Failed Test Suites: 0
[2025-06-21T10:42:00.814Z] Total Tests: 52
[2025-06-21T10:42:00.814Z] Passed Tests: 24
[2025-06-21T10:42:00.814Z] Failed Tests: 0
[2025-06-21T10:42:00.814Z] Skipped Tests: 28
[2025-06-21T10:42:00.814Z] Success Rate: 46.15%
[2025-06-21T10:42:00.814Z] Overall Result: FAILURE
[2025-06-21T10:42:00.814Z] ================================================================================
