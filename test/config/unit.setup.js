/**
 * 单元测试专用setup配置
 * 遵循best-practices.md的Mock使用策略
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.MONGODB_URI = 'mongodb://localhost:27017/firespoon_test';
process.env.REDIS_URL = 'redis://localhost:6379';

// 全局Mock设置 - 遵循最佳实践
beforeEach(() => {
  // 清理所有mock
  jest.clearAllMocks();
  
  // 重置模块缓存
  jest.resetModules();
});

afterEach(() => {
  // 恢复所有mock
  jest.restoreAllMocks();
});

// 全局测试工具
global.mockObjectId = (id) => id || 'mock-object-id-' + Math.random().toString(36).substr(2, 9);

// Mock console方法以减少测试输出噪音
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// 设置Jest超时
jest.setTimeout(10000);

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
