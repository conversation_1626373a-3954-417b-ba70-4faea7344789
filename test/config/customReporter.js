/**
 * 自定义Jest报告器
 * 符合SOP要求的测试结果输出格式
 */

const fs = require('fs');
const path = require('path');

class CustomReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options;
    this.outputFile = options.outputFile || path.join(__dirname, '../reports/test-summary.json');
    this.startTime = Date.now();
    this.testResults = {
      summary: {},
      projects: {},
      coverage: {},
      performance: {}
    };
  }

  onRunStart(results, options) {
    console.log('\n🚀 Firespoon API 测试框架启动');
    console.log('=' * 50);
    this.startTime = Date.now();
  }

  onTestResult(test, testResult, aggregatedResult) {
    const projectName = testResult.displayName?.name || 'Unknown';
    
    if (!this.testResults.projects[projectName]) {
      this.testResults.projects[projectName] = {
        tests: [],
        summary: {
          total: 0,
          passed: 0,
          failed: 0,
          skipped: 0,
          duration: 0
        }
      };
    }

    const project = this.testResults.projects[projectName];
    
    testResult.testResults.forEach(result => {
      project.tests.push({
        title: result.title,
        status: result.status,
        duration: result.duration,
        failureMessages: result.failureMessages,
        ancestorTitles: result.ancestorTitles
      });
      
      project.summary.total++;
      project.summary.duration += result.duration || 0;
      
      switch (result.status) {
        case 'passed':
          project.summary.passed++;
          break;
        case 'failed':
          project.summary.failed++;
          break;
        case 'skipped':
        case 'pending':
          project.summary.skipped++;
          break;
      }
    });
  }

  onRunComplete(contexts, results) {
    const duration = Date.now() - this.startTime;
    
    // 生成总结报告
    this.testResults.summary = {
      totalTests: results.numTotalTests,
      passedTests: results.numPassedTests,
      failedTests: results.numFailedTests,
      skippedTests: results.numPendingTests,
      totalDuration: duration,
      success: results.success,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown'
    };

    // 覆盖率信息
    if (results.coverageMap) {
      this.testResults.coverage = {
        statements: results.coverageMap.getCoverageSummary().statements,
        branches: results.coverageMap.getCoverageSummary().branches,
        functions: results.coverageMap.getCoverageSummary().functions,
        lines: results.coverageMap.getCoverageSummary().lines
      };
    }

    // 输出控制台报告
    this._printConsoleReport();
    
    // 保存JSON报告
    this._saveJsonReport();
  }

  _printConsoleReport() {
    const { summary, projects } = this.testResults;

    console.log('\n' + '='.repeat(80));
    console.log('📊 FIRESPOON API 测试执行报告 (Test Execution Report)');
    console.log('='.repeat(80));

    // 总体统计
    console.log('\n📈 总体统计 (Overall Statistics):');
    console.log(`   总测试用例数 (Total Cases): ${summary.totalTests}`);
    console.log(`   ✅ 通过 (Passed): ${summary.passedTests}`);
    console.log(`   ❌ 失败 (Failed): ${summary.failedTests}`);
    console.log(`   ⏭️  跳过 (Skipped): ${summary.skippedTests}`);
    console.log(`   ⏱️  总执行时间 (Total Duration): ${(summary.totalDuration / 1000).toFixed(2)}s`);
    console.log(`   🎯 成功率 (Success Rate): ${((summary.passedTests / summary.totalTests) * 100).toFixed(2)}%`);

    // 按测试类型分组显示 - 符合SOP格式
    console.log('\n📋 分类测试结果 (Test Results by Category):');
    Object.entries(projects).forEach(([projectName, project]) => {
      const successRate = project.summary.total > 0
        ? ((project.summary.passed / project.summary.total) * 100).toFixed(2)
        : 0;

      console.log(`\n${this._getProjectIcon(projectName)} ${projectName} 测试结果:`);
      console.log(`   状态 (Status): ${project.summary.failed === 0 ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`   用例统计 (Case Statistics): ${project.summary.passed}/${project.summary.total} (${successRate}%)`);
      console.log(`   执行时间 (Duration): ${(project.summary.duration / 1000).toFixed(2)}s`);

      // 显示测试用例详情 - 按照YAML格式
      if (project.tests.length > 0) {
        console.log(`   测试用例详情 (Test Case Details):`);
        project.tests.forEach((test, index) => {
          const caseId = this._generateCaseId(projectName, index + 1);
          const status = test.status === 'passed' ? '✅ PASS' :
                        test.status === 'failed' ? '❌ FAIL' : '⏭️ SKIP';
          const module = this._extractModuleFromPath(test.ancestorTitles);

          console.log(`     - CaseID: "${caseId}"`);
          console.log(`       Module: "${module}"`);
          console.log(`       Description: "${test.title}"`);
          console.log(`       Status: ${status}`);
          console.log(`       Duration: ${test.duration || 0}ms`);

          if (test.status === 'failed' && test.failureMessages.length > 0) {
            console.log(`       FailureReason: "${test.failureMessages[0].split('\n')[0]}"`);
          }
          console.log('');
        });
      }
    });

    // 覆盖率报告 - 符合SOP格式
    if (this.testResults.coverage && Object.keys(this.testResults.coverage).length > 0) {
      console.log('\n📈 代码覆盖率报告 (Code Coverage Report):');
      Object.entries(this.testResults.coverage).forEach(([type, coverage]) => {
        const pct = coverage.pct || 0;
        const status = pct >= 80 ? '✅ EXCELLENT' : pct >= 60 ? '⚠️ ACCEPTABLE' : '❌ NEEDS_IMPROVEMENT';
        console.log(`   ${type.toUpperCase()}: ${pct}% (${coverage.covered}/${coverage.total}) - ${status}`);
      });
    }

    // 最终状态
    console.log('\n' + '='.repeat(80));
    const finalStatus = summary.success ? '🎉 测试执行成功 (TEST EXECUTION SUCCESSFUL)' : '💥 测试执行失败 (TEST EXECUTION FAILED)';
    console.log(finalStatus);
    console.log('='.repeat(80));
  }

  _getProjectIcon(projectName) {
    const icons = {
      'UNIT': '🔧',
      'INTEGRATION': '🔗',
      'E2E': '🎭',
      'PERFORMANCE': '⚡'
    };
    return icons[projectName] || '📝';
  }

  _generateCaseId(projectName, index) {
    const prefixes = {
      'UNIT': 'U',
      'INTEGRATION': 'I',
      'E2E': 'E',
      'PERFORMANCE': 'P'
    };
    const prefix = prefixes[projectName] || 'T';
    return `${prefix}-AUTO-${String(index).padStart(3, '0')}`;
  }

  _extractModuleFromPath(ancestorTitles) {
    if (!ancestorTitles || ancestorTitles.length === 0) {
      return 'unknown';
    }

    // 从测试路径中提取模块名
    const title = ancestorTitles[0];

    // 常见模块映射
    const moduleMap = {
      'WhatsAppService': 'whatsapp',
      'OrderService': 'order',
      'PaymentService': 'payment',
      'RefundService': 'refund',
      'CustomerService': 'customer',
      'RestaurantService': 'restaurant',
      'GraphQL': 'graphql',
      'Order': 'order',
      'Customer': 'customer',
      'Restaurant': 'restaurant',
      'Payment': 'payment',
      'Refund': 'refund'
    };

    // 尝试从标题中匹配模块
    for (const [key, module] of Object.entries(moduleMap)) {
      if (title.includes(key)) {
        return module;
      }
    }

    // 如果没有匹配，返回小写的第一个词
    return title.toLowerCase().split(' ')[0];
  }

  _saveJsonReport() {
    try {
      const outputDir = path.dirname(this.outputFile);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      fs.writeFileSync(this.outputFile, JSON.stringify(this.testResults, null, 2));
      console.log(`\n📄 详细报告已保存: ${this.outputFile}`);

      // 同时保存为test-summary.json以保持向后兼容
      const summaryFile = path.join(outputDir, 'test-summary.json');
      fs.writeFileSync(summaryFile, JSON.stringify(this.testResults, null, 2));
    } catch (error) {
      console.error('❌ 保存测试报告失败:', error.message);
    }
  }
}

module.exports = CustomReporter;
